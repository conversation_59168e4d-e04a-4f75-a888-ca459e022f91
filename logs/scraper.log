{"timestamp":"2025-06-30T10:49:35.198Z","level":"INFO","instanceId":"health_monitor_72766","message":"✅ Health Monitor initialized","components":[]}
{"timestamp":"2025-06-30T10:49:35.543Z","level":"WARN","instanceId":"logger_72766","message":"内存使用率过高: 99.0%","type":"memory","severity":"warning","messageEn":"High memory usage: 99.0%","threshold":10,"actual":98.97,"action":"monitor_only","note":"仅监控模式 - 不会退出实例"}
{"timestamp":"2025-06-30T10:49:35.551Z","level":"INFO","instanceId":"logger_72766","message":"📊 详细内存信息","进程堆内存已用":"3.76 MB","进程堆内存总计":"4.30 MB","进程RSS":"32.81 MB","系统内存总计":"18.00 GB","系统内存已用":"17.82 GB","系统内存空闲":"0.18 GB","系统使用率":"98.97%","进程使用率":"87.54%"}
{"timestamp":"2025-06-30T10:49:35.543Z","level":"WARN","instanceId":"logger_72766","message":"CPU使用率过高: 25.2%","type":"cpu","severity":"warning","messageEn":"High CPU usage: 25.2%","threshold":10,"actual":25.18486646884273,"action":"monitor_only","note":"仅监控模式 - 不会退出实例"}
{"timestamp":"2025-06-30T10:49:35.568Z","level":"INFO","instanceId":"logger_72766","message":"📊 详细CPU信息","用户态CPU":"15.13%","系统态CPU":"10.05%","总CPU使用率":"25.18%","CPU核心数":11,"系统负载":"18.98, 18.47, 16.08"}
{"timestamp":"2025-06-30T10:58:14.773Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"✅ Core components initialized"}
{"timestamp":"2025-06-30T10:58:14.882Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🔧 TokenLifecycleManager configuration","environment":"development","ignoreTokenExpiration":true,"tokensFile":"./tokens.json"}
{"timestamp":"2025-06-30T10:58:14.891Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📥 Tokens loaded successfully","totalTokens":17,"validTokens":17}
{"timestamp":"2025-06-30T10:58:14.893Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🔄 Startup token validation completed","markedValid":17,"total":17,"note":"All tokens with valid JWT structure marked as valid at startup"}
{"timestamp":"2025-06-30T10:58:14.894Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"✅ Token Lifecycle Manager initialized","totalTokens":17,"validTokens":17,"note":"All tokens marked as valid at startup"}
{"timestamp":"2025-06-30T10:58:14.945Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📥 Tokens loaded successfully","totalTokens":17,"validTokens":17}
{"timestamp":"2025-06-30T10:58:14.947Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🔄 Startup token validation completed","markedValid":17,"total":17,"note":"All tokens with valid JWT structure marked as valid at startup"}
{"timestamp":"2025-06-30T10:58:14.948Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"✅ Token Lifecycle Manager initialized","totalTokens":17,"validTokens":17,"note":"All tokens marked as valid at startup"}
{"timestamp":"2025-06-30T10:58:14.963Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📥 Parameters loaded","count":1539}
{"timestamp":"2025-06-30T10:58:14.973Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📥 Parameters loaded","count":1539}
{"timestamp":"2025-06-30T10:58:14.999Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📥 Progress loaded","paramStates":1539,"instances":14}
{"timestamp":"2025-06-30T10:58:15.091Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📥 Progress loaded","paramStates":1539,"instances":14}
{"timestamp":"2025-06-30T10:58:15.201Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📝 Instance registered"}
{"timestamp":"2025-06-30T10:58:15.202Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📝 Instance registered"}
{"timestamp":"2025-06-30T10:58:15.203Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"✅ Parameter State Manager initialized","totalParams":1539,"pendingParams":1208}
{"timestamp":"2025-06-30T10:58:15.204Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"✅ Parameter State Manager initialized","totalParams":1539,"pendingParams":1208}
{"timestamp":"2025-06-30T10:58:15.204Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🚀 正在初始化增强版Playwright爬虫"}
{"timestamp":"2025-06-30T10:58:17.359Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🌐 浏览器已启动","headless":false,"version":"138.0.7204.23"}
{"timestamp":"2025-06-30T10:58:17.367Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"✅ 增强版Playwright爬虫初始化完成"}
{"timestamp":"2025-06-30T10:58:17.368Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"✅ Management components initialized"}
{"timestamp":"2025-06-30T10:58:17.378Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"✅ Health Monitor initialized","components":["tokenManager","parameterManager","crawler"]}
{"timestamp":"2025-06-30T10:58:17.379Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"✅ Monitoring initialized"}
{"timestamp":"2025-06-30T10:58:17.380Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"✅ Scraping Orchestrator initialized successfully"}
{"timestamp":"2025-06-30T10:58:17.381Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🎯 Starting scraping process"}
{"timestamp":"2025-06-30T10:58:17.382Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🔧 Instance count check","activeInstances":6,"maxConcurrentInstances":10,"totalInstances":15,"currentInstanceId":"instance_76459_1751281094768"}
{"timestamp":"2025-06-30T10:58:17.433Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📦 Batch assigned","batchSize":10,"remaining":1188}
{"timestamp":"2025-06-30T10:58:17.436Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📦 Processing batch","batchSize":10,"remaining":1198}
{"timestamp":"2025-06-30T10:58:17.439Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🎯 Processing parameter 1/10","param":"200_1_198_144605__"}
{"timestamp":"2025-06-30T10:58:17.441Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🚀 Starting operation: process_parameter","operation":"process_parameter","operationId":"op_1751281097440_rrjkn","phase":"start","paramKey":"200_1_198_144605__"}
{"timestamp":"2025-06-30T10:58:17.443Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🎯 开始处理参数","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"六年级上","catalogName":"第七单元 "}}
{"timestamp":"2025-06-30T10:58:17.445Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📂 设置当前任务组合","combination":"初中/语文/部编版(五四制)(2024)/六年级上/第七单元 ","codes":{"studyPhaseCode":"200","subjectCode":"1","textbookVersionCode":"198","ceciCode":"172","catalogCode":"144605"}}
{"timestamp":"2025-06-30T10:58:17.448Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🔍 正在使用令牌[111]创建浏览器并验证..."}
{"timestamp":"2025-06-30T10:58:17.485Z","level":"WARN","instanceId":"instance_76459_1751281094768","message":"内存使用率过高: 99.1%","type":"memory","severity":"warning","messageEn":"High memory usage: 99.1%","threshold":80,"actual":99.1,"action":"monitor_only","note":"仅监控模式 - 不会退出实例"}
{"timestamp":"2025-06-30T10:58:17.489Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📊 详细内存信息","进程堆内存已用":"22.90 MB","进程堆内存总计":"37.38 MB","进程RSS":"47.48 MB","系统内存总计":"18.00 GB","系统内存已用":"17.84 GB","系统内存空闲":"0.16 GB","系统使用率":"99.10%","进程使用率":"61.28%"}
{"timestamp":"2025-06-30T10:58:17.508Z","level":"WARN","instanceId":"instance_76459_1751281094768","message":"🔌 浏览器已断开连接"}
{"timestamp":"2025-06-30T10:58:18.196Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🌐 浏览器已启动","headless":false,"version":"138.0.7204.23"}
{"timestamp":"2025-06-30T10:58:19.908Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"✅ 已成功创建浏览器和页面并注入令牌","tokenId":111,"pageId":"page_1751281099907_3oy0b","contextId":"context_1751281098198_6ru8a"}
{"timestamp":"2025-06-30T10:58:19.911Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"✅ 浏览器实例创建成功，开始页面爬取"}
{"timestamp":"2025-06-30T10:58:19.914Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📂 输出目录已创建","encodedPath":"output/%E5%88%9D%E4%B8%AD/%E8%AF%AD%E6%96%87/%E9%83%A8%E7%BC%96%E7%89%88%28%E4%BA%94%E5%9B%9B%E5%88%B6%29%282024%29/%E5%85%AD%E5%B9%B4%E7%BA%A7%E4%B8%8A/%E7%AC%AC%E4%B8%83%E5%8D%95%E5%85%83%20","originalPath":"初中/语文/部编版(五四制)(2024)/六年级上/第七单元 ","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"六年级上","catalogName":"第七单元 "}}
{"timestamp":"2025-06-30T10:58:30.315Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🔧 配置教材版本和册次设置..."}
{"timestamp":"2025-06-30T10:58:34.008Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📌 选择教材版本: 部编版(五四制)(2024)"}
{"timestamp":"2025-06-30T10:58:34.706Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📌 选择册次: 六年级上"}
{"timestamp":"2025-06-30T10:58:36.708Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📂 选择目录: 第七单元 "}
{"timestamp":"2025-06-30T10:58:38.857Z","level":"ERROR","instanceId":"instance_76459_1751281094768","message":"❌ 目录选择失败","error":"未找到目录: 第七单元 "}
{"timestamp":"2025-06-30T10:58:38.859Z","level":"ERROR","instanceId":"instance_76459_1751281094768","message":"❌ 设置配置失败","error":"未找到目录: 第七单元 "}
{"timestamp":"2025-06-30T10:58:38.860Z","level":"ERROR","instanceId":"instance_76459_1751281094768","message":"❌ 处理第1页时出错","error":"未找到目录: 第七单元 ","parameters":{"studyPhaseCode":"200","studyPhaseName":"初中","subjectCode":"1","subjectName":"语文","textbookVersionCode":"198","textbookVersionName":"部编版(五四制)(2024)","ceciCode":"172","ceciName":"六年级上","gradeCode":"13","catalogCode":"144605","catalogName":"第七单元 ","key":"200_1_198_144605__","id":320}}
{"timestamp":"2025-06-30T10:58:38.860Z","level":"ERROR","instanceId":"instance_76459_1751281094768","message":"❌ 参数处理失败","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"六年级上","catalogName":"第七单元 "},"error":"未找到目录: 第七单元 ","duration":"21.42 seconds"}
{"timestamp":"2025-06-30T10:58:39.012Z","level":"WARN","instanceId":"instance_76459_1751281094768","message":"🔌 浏览器已断开连接"}
{"timestamp":"2025-06-30T10:58:39.219Z","level":"ERROR","instanceId":"instance_76459_1751281094768","message":"❌ Failed operation: process_parameter","operation":"process_parameter","operationId":"op_1751281097440_rrjkn","phase":"failed","error":"未找到目录: 第七单元 ","paramKey":"200_1_198_144605__"}
{"timestamp":"2025-06-30T10:58:39.295Z","level":"WARN","instanceId":"instance_76459_1751281094768","message":"❌ Parameter failed","paramKey":"200_1_198_144605__","error":"未找到目录: 第七单元 ","attempts":2}
{"timestamp":"2025-06-30T10:58:39.296Z","level":"ERROR","instanceId":"instance_76459_1751281094768","message":"❌ Parameter failed","param":"200_1_198_144605__","error":"未找到目录: 第七单元 "}
{"timestamp":"2025-06-30T10:58:39.296Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🎯 Processing parameter 2/10","param":"200_1_198_149921__"}
{"timestamp":"2025-06-30T10:58:39.296Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🚀 Starting operation: process_parameter","operation":"process_parameter","operationId":"op_1751281119296_ztaw6","phase":"start","paramKey":"200_1_198_149921__"}
{"timestamp":"2025-06-30T10:58:39.296Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🎯 开始处理参数","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"六年级下","catalogName":"第四单元活动·探究"}}
{"timestamp":"2025-06-30T10:58:39.296Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📂 设置当前任务组合","combination":"初中/语文/部编版(五四制)(2024)/六年级下/第四单元活动·探究","codes":{"studyPhaseCode":"200","subjectCode":"1","textbookVersionCode":"198","ceciCode":"173","catalogCode":"149921"}}
{"timestamp":"2025-06-30T10:58:39.296Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🔍 正在使用令牌[111]创建浏览器并验证..."}
{"timestamp":"2025-06-30T10:58:40.443Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🌐 浏览器已启动","headless":false,"version":"138.0.7204.23"}
{"timestamp":"2025-06-30T10:58:41.173Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"✅ 已成功创建浏览器和页面并注入令牌","tokenId":111,"pageId":"page_1751281121172_z60v5","contextId":"context_1751281120455_ums40"}
{"timestamp":"2025-06-30T10:58:41.175Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"✅ 浏览器实例创建成功，开始页面爬取"}
{"timestamp":"2025-06-30T10:58:41.181Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📂 输出目录已创建","encodedPath":"output/%E5%88%9D%E4%B8%AD/%E8%AF%AD%E6%96%87/%E9%83%A8%E7%BC%96%E7%89%88%28%E4%BA%94%E5%9B%9B%E5%88%B6%29%282024%29/%E5%85%AD%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%9B%9B%E5%8D%95%E5%85%83%E6%B4%BB%E5%8A%A8%C2%B7%E6%8E%A2%E7%A9%B6","originalPath":"初中/语文/部编版(五四制)(2024)/六年级下/第四单元活动·探究","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"六年级下","catalogName":"第四单元活动·探究"}}
{"timestamp":"2025-06-30T10:58:46.640Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🔧 配置教材版本和册次设置..."}
{"timestamp":"2025-06-30T10:58:50.055Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📌 选择教材版本: 部编版(五四制)(2024)"}
{"timestamp":"2025-06-30T10:58:50.622Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📌 选择册次: 六年级下"}
{"timestamp":"2025-06-30T10:58:51.299Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🔄 Logger shutting down..."}
{"timestamp":"2025-06-30T10:58:51.301Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🔄 Received SIGINT, initiating graceful shutdown..."}
{"timestamp":"2025-06-30T10:58:51.303Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🔄 Shutting down Scraping Orchestrator..."}
{"timestamp":"2025-06-30T10:58:51.303Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🛑 Stopping scraping process..."}
{"timestamp":"2025-06-30T10:58:51.323Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🔄 Logger shutting down..."}
{"timestamp":"2025-06-30T11:10:54.465Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ Core components initialized"}
{"timestamp":"2025-06-30T11:10:54.489Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🔧 TokenLifecycleManager configuration","environment":"development","ignoreTokenExpiration":true,"tokensFile":"./tokens.json"}
{"timestamp":"2025-06-30T11:10:54.490Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📥 Tokens loaded successfully","totalTokens":17,"validTokens":17}
{"timestamp":"2025-06-30T11:10:54.490Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🔄 Startup token validation completed","markedValid":17,"total":17,"note":"All tokens with valid JWT structure marked as valid at startup"}
{"timestamp":"2025-06-30T11:10:54.491Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ Token Lifecycle Manager initialized","totalTokens":17,"validTokens":17,"note":"All tokens marked as valid at startup"}
{"timestamp":"2025-06-30T11:10:54.541Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📥 Tokens loaded successfully","totalTokens":17,"validTokens":17}
{"timestamp":"2025-06-30T11:10:54.541Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🔄 Startup token validation completed","markedValid":17,"total":17,"note":"All tokens with valid JWT structure marked as valid at startup"}
{"timestamp":"2025-06-30T11:10:54.542Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ Token Lifecycle Manager initialized","totalTokens":17,"validTokens":17,"note":"All tokens marked as valid at startup"}
{"timestamp":"2025-06-30T11:10:54.545Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📥 Parameters loaded","count":1539}
{"timestamp":"2025-06-30T11:10:54.547Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📥 Parameters loaded","count":1539}
{"timestamp":"2025-06-30T11:10:54.555Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📥 Progress loaded","paramStates":1539,"instances":15}
{"timestamp":"2025-06-30T11:10:54.561Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📥 Progress loaded","paramStates":1539,"instances":15}
{"timestamp":"2025-06-30T11:10:54.572Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📝 Instance registered"}
{"timestamp":"2025-06-30T11:10:54.572Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📝 Instance registered"}
{"timestamp":"2025-06-30T11:10:54.573Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ Parameter State Manager initialized","totalParams":1539,"pendingParams":1199}
{"timestamp":"2025-06-30T11:10:54.573Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ Parameter State Manager initialized","totalParams":1539,"pendingParams":1199}
{"timestamp":"2025-06-30T11:10:54.573Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🚀 正在初始化增强版Playwright爬虫"}
{"timestamp":"2025-06-30T11:10:55.126Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🌐 浏览器已启动","headless":false,"version":"138.0.7204.23"}
{"timestamp":"2025-06-30T11:10:55.126Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ 增强版Playwright爬虫初始化完成"}
{"timestamp":"2025-06-30T11:10:55.126Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ Management components initialized"}
{"timestamp":"2025-06-30T11:10:55.127Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ Health Monitor initialized","components":["tokenManager","parameterManager","crawler"]}
{"timestamp":"2025-06-30T11:10:55.127Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ Monitoring initialized"}
{"timestamp":"2025-06-30T11:10:55.127Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ Scraping Orchestrator initialized successfully"}
{"timestamp":"2025-06-30T11:10:55.127Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🎯 Starting scraping process"}
{"timestamp":"2025-06-30T11:10:55.127Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🔧 Instance count check","activeInstances":5,"maxConcurrentInstances":10,"totalInstances":16,"currentInstanceId":"instance_6558_1751281854464"}
{"timestamp":"2025-06-30T11:10:55.127Z","level":"WARN","instanceId":"instance_65422_1751279845110","message":"🧹 Cleaned up stale instance","cleanedParams":8}
{"timestamp":"2025-06-30T11:10:55.127Z","level":"WARN","instanceId":"instance_66210_1751279916132","message":"🧹 Cleaned up stale instance","cleanedParams":9}
{"timestamp":"2025-06-30T11:10:55.127Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🧹 Cleanup completed","cleanedParams":16,"processedInstances":2}
{"timestamp":"2025-06-30T11:10:55.134Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📦 Batch assigned","batchSize":10,"remaining":1195}
{"timestamp":"2025-06-30T11:10:55.134Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📦 Processing batch","batchSize":10,"remaining":1205}
{"timestamp":"2025-06-30T11:10:55.134Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🎯 Processing parameter 1/10","param":"200_1_2_103775__"}
{"timestamp":"2025-06-30T11:10:55.134Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🚀 Starting operation: process_parameter","operation":"process_parameter","operationId":"op_1751281855134_wk1vs","phase":"start","paramKey":"200_1_2_103775__"}
{"timestamp":"2025-06-30T11:10:55.135Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🎯 开始处理参数","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版","ceciName":"八年级下","catalogName":"第五单元"}}
{"timestamp":"2025-06-30T11:10:55.135Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📂 设置当前任务组合","combination":"初中/语文/部编版/八年级下/第五单元","codes":{"studyPhaseCode":"200","subjectCode":"1","textbookVersionCode":"2","ceciCode":"82","catalogCode":"103775"}}
{"timestamp":"2025-06-30T11:10:55.135Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🔍 正在使用令牌[111]创建浏览器并验证..."}
{"timestamp":"2025-06-30T11:10:55.153Z","level":"WARN","instanceId":"instance_6558_1751281854464","message":"🔌 浏览器已断开连接"}
{"timestamp":"2025-06-30T11:10:55.229Z","level":"WARN","instanceId":"instance_6558_1751281854464","message":"内存使用率过高: 97.7%","type":"memory","severity":"warning","messageEn":"High memory usage: 97.7%","threshold":80,"actual":97.72,"action":"monitor_only","note":"仅监控模式 - 不会退出实例"}
{"timestamp":"2025-06-30T11:10:55.229Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📊 详细内存信息","进程堆内存已用":"22.87 MB","进程堆内存总计":"37.64 MB","进程RSS":"99.70 MB","系统内存总计":"18.00 GB","系统内存已用":"17.59 GB","系统内存空闲":"0.41 GB","系统使用率":"97.72%","进程使用率":"60.76%"}
{"timestamp":"2025-06-30T11:10:55.385Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🌐 浏览器已启动","headless":false,"version":"138.0.7204.23"}
{"timestamp":"2025-06-30T11:10:55.650Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ 已成功创建浏览器和页面并注入令牌","tokenId":111,"pageId":"page_1751281855650_gju3h","contextId":"context_1751281855386_8ba31"}
{"timestamp":"2025-06-30T11:10:55.651Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ 浏览器实例创建成功，开始页面爬取"}
{"timestamp":"2025-06-30T11:10:55.651Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📂 输出目录已创建","encodedPath":"output/%E5%88%9D%E4%B8%AD/%E8%AF%AD%E6%96%87/%E9%83%A8%E7%BC%96%E7%89%88/%E5%85%AB%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E4%BA%94%E5%8D%95%E5%85%83","originalPath":"初中/语文/部编版/八年级下/第五单元","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版","ceciName":"八年级下","catalogName":"第五单元"}}
{"timestamp":"2025-06-30T11:11:00.716Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🔧 配置教材版本和册次设置..."}
{"timestamp":"2025-06-30T11:11:04.167Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📌 选择教材版本: 部编版"}
{"timestamp":"2025-06-30T11:11:04.684Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📌 选择册次: 八年级下"}
{"timestamp":"2025-06-30T11:11:06.645Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📂 选择目录: 第五单元"}
{"timestamp":"2025-06-30T11:11:10.729Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ 目录选择完成: 第五单元"}
{"timestamp":"2025-06-30T11:11:10.730Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ 教材版本、册次和目录设置完成"}
{"timestamp":"2025-06-30T11:11:10.759Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📄 已更新最大页数为: 57"}
{"timestamp":"2025-06-30T11:11:40.761Z","level":"WARN","instanceId":"instance_6558_1751281854464","message":"⏰ 第1页响应捕获超时，跳过"}
{"timestamp":"2025-06-30T11:11:40.762Z","level":"WARN","instanceId":"instance_6558_1751281854464","message":"⏰ 第1页响应超时"}
{"timestamp":"2025-06-30T11:11:40.763Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ 参数处理完成","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版","ceciName":"八年级下","catalogName":"第五单元"},"totalCount":0,"pagesProcessed":0,"duration":"45.63 seconds"}
{"timestamp":"2025-06-30T11:11:40.816Z","level":"WARN","instanceId":"instance_6558_1751281854464","message":"🔌 浏览器已断开连接"}
{"timestamp":"2025-06-30T11:11:40.898Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ Completed operation: process_parameter","operation":"process_parameter","operationId":"op_1751281855134_wk1vs","phase":"complete","paramKey":"200_1_2_103775__","dataSize":0,"pagesProcessed":0,"savedPath":"output/%E5%88%9D%E4%B8%AD/%E8%AF%AD%E6%96%87/%E9%83%A8%E7%BC%96%E7%89%88/%E5%85%AB%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E4%BA%94%E5%8D%95%E5%85%83"}
{"timestamp":"2025-06-30T11:11:40.906Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ Parameter completed","paramKey":"200_1_2_103775__"}
{"timestamp":"2025-06-30T11:11:40.906Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ Parameter completed","param":"200_1_2_103775__","dataSize":0,"pagesProcessed":0}
{"timestamp":"2025-06-30T11:11:40.906Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🎯 Processing parameter 2/10","param":"200_1_2_103783__"}
{"timestamp":"2025-06-30T11:11:40.906Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🚀 Starting operation: process_parameter","operation":"process_parameter","operationId":"op_1751281900906_fcyha","phase":"start","paramKey":"200_1_2_103783__"}
{"timestamp":"2025-06-30T11:11:40.906Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🎯 开始处理参数","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版","ceciName":"八年级下","catalogName":"第六单元"}}
{"timestamp":"2025-06-30T11:11:40.906Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📂 设置当前任务组合","combination":"初中/语文/部编版/八年级下/第六单元","codes":{"studyPhaseCode":"200","subjectCode":"1","textbookVersionCode":"2","ceciCode":"82","catalogCode":"103783"}}
{"timestamp":"2025-06-30T11:11:40.906Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🔍 正在使用令牌[111]创建浏览器并验证..."}
{"timestamp":"2025-06-30T11:11:41.156Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🌐 浏览器已启动","headless":false,"version":"138.0.7204.23"}
{"timestamp":"2025-06-30T11:11:41.386Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ 已成功创建浏览器和页面并注入令牌","tokenId":111,"pageId":"page_1751281901386_ibwwe","contextId":"context_1751281901157_2xc6l"}
{"timestamp":"2025-06-30T11:11:41.387Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ 浏览器实例创建成功，开始页面爬取"}
{"timestamp":"2025-06-30T11:11:41.387Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📂 输出目录已创建","encodedPath":"output/%E5%88%9D%E4%B8%AD/%E8%AF%AD%E6%96%87/%E9%83%A8%E7%BC%96%E7%89%88/%E5%85%AB%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%85%AD%E5%8D%95%E5%85%83","originalPath":"初中/语文/部编版/八年级下/第六单元","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版","ceciName":"八年级下","catalogName":"第六单元"}}
{"timestamp":"2025-06-30T11:11:46.246Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🔧 配置教材版本和册次设置..."}
{"timestamp":"2025-06-30T11:11:49.658Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📌 选择教材版本: 部编版"}
{"timestamp":"2025-06-30T11:11:55.131Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📊 Status Report","parameters":{"total":1539,"completed":40,"pending":1205,"processing":18,"failed":276,"completionRate":"2.6%"},"tokens":{"total":17,"valid":17,"healthy":17},"crawler":{"successRate":"0.0%","averageResponseTime":"0ms"},"health":{"overall":"warning","alerts":1},"processing":{"totalProcessed":1,"successCount":1,"errorCount":0,"currentBatchSize":10}}
{"timestamp":"2025-06-30T11:12:37.493Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🔄 Logger shutting down..."}
{"timestamp":"2025-06-30T11:12:37.495Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🔄 Received SIGINT, initiating graceful shutdown..."}
{"timestamp":"2025-06-30T11:12:37.496Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🔄 Shutting down Scraping Orchestrator..."}
{"timestamp":"2025-06-30T11:12:37.496Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🛑 Stopping scraping process..."}
{"timestamp":"2025-06-30T11:12:37.522Z","level":"ERROR","instanceId":"instance_6558_1751281854464","message":"❌ 设置配置失败","error":"locator.click: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - waiting for locator('.ant-dropdown-content').first().locator('.flex_warp').first().locator('.pointer').nth(1)\u001b[22m\n\u001b[2m    - locator resolved to <p data-v-46b589e4=\"\" class=\"pl_10 pr_10 lh_24 ml_20 bd_radius_2 mt_20 pointer bg_shadow_yellow tc_yellow\"> 部编版 </p>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is not stable\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m    - element is not stable\u001b[22m\n\u001b[2m  2 × retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m      - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is not visible\u001b[22m\n\u001b[2m  91 × retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\u001b[2m       - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is not visible\u001b[22m\n\u001b[2m  - retrying click action\u001b[22m\n\u001b[2m    - waiting 500ms\u001b[22m\n"}
{"timestamp":"2025-06-30T11:12:37.524Z","level":"ERROR","instanceId":"instance_6558_1751281854464","message":"❌ 处理第1页时出错","error":"locator.click: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - waiting for locator('.ant-dropdown-content').first().locator('.flex_warp').first().locator('.pointer').nth(1)\u001b[22m\n\u001b[2m    - locator resolved to <p data-v-46b589e4=\"\" class=\"pl_10 pr_10 lh_24 ml_20 bd_radius_2 mt_20 pointer bg_shadow_yellow tc_yellow\"> 部编版 </p>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is not stable\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m    - element is not stable\u001b[22m\n\u001b[2m  2 × retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m      - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is not visible\u001b[22m\n\u001b[2m  91 × retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\u001b[2m       - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is not visible\u001b[22m\n\u001b[2m  - retrying click action\u001b[22m\n\u001b[2m    - waiting 500ms\u001b[22m\n","parameters":{"studyPhaseCode":"200","studyPhaseName":"初中","subjectCode":"1","subjectName":"语文","textbookVersionCode":"2","textbookVersionName":"部编版","ceciCode":"82","ceciName":"八年级下","gradeCode":"8","catalogCode":"103783","catalogName":"第六单元","key":"200_1_2_103783__","id":307}}
{"timestamp":"2025-06-30T11:12:37.526Z","level":"ERROR","instanceId":"instance_6558_1751281854464","message":"❌ 参数处理失败","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版","ceciName":"八年级下","catalogName":"第六单元"},"error":"locator.click: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - waiting for locator('.ant-dropdown-content').first().locator('.flex_warp').first().locator('.pointer').nth(1)\u001b[22m\n\u001b[2m    - locator resolved to <p data-v-46b589e4=\"\" class=\"pl_10 pr_10 lh_24 ml_20 bd_radius_2 mt_20 pointer bg_shadow_yellow tc_yellow\"> 部编版 </p>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is not stable\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m    - element is not stable\u001b[22m\n\u001b[2m  2 × retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m      - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is not visible\u001b[22m\n\u001b[2m  91 × retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\u001b[2m       - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is not visible\u001b[22m\n\u001b[2m  - retrying click action\u001b[22m\n\u001b[2m    - waiting 500ms\u001b[22m\n","duration":"56.62 seconds"}
{"timestamp":"2025-06-30T11:12:37.545Z","level":"WARN","instanceId":"instance_6558_1751281854464","message":"🔌 浏览器已断开连接"}
{"timestamp":"2025-06-30T11:12:37.558Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🔄 Logger shutting down..."}
{"timestamp":"2025-06-30T11:24:27.553Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ Core components initialized"}
{"timestamp":"2025-06-30T11:24:27.578Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🔧 TokenLifecycleManager configuration","environment":"development","ignoreTokenExpiration":true,"tokensFile":"./tokens.json"}
{"timestamp":"2025-06-30T11:24:27.579Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📥 Tokens loaded successfully","totalTokens":17,"validTokens":17}
{"timestamp":"2025-06-30T11:24:27.579Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🔄 Startup token validation completed","markedValid":17,"total":17,"note":"All tokens with valid JWT structure marked as valid at startup"}
{"timestamp":"2025-06-30T11:24:27.579Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ Token Lifecycle Manager initialized","totalTokens":17,"validTokens":17,"note":"All tokens marked as valid at startup"}
{"timestamp":"2025-06-30T11:24:27.630Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📥 Tokens loaded successfully","totalTokens":17,"validTokens":17}
{"timestamp":"2025-06-30T11:24:27.630Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🔄 Startup token validation completed","markedValid":17,"total":17,"note":"All tokens with valid JWT structure marked as valid at startup"}
{"timestamp":"2025-06-30T11:24:27.631Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ Token Lifecycle Manager initialized","totalTokens":17,"validTokens":17,"note":"All tokens marked as valid at startup"}
{"timestamp":"2025-06-30T11:24:27.634Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📥 Parameters loaded","count":1539}
{"timestamp":"2025-06-30T11:24:27.637Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📥 Parameters loaded","count":1539}
{"timestamp":"2025-06-30T11:24:27.644Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📥 Progress loaded","paramStates":1539,"instances":16}
{"timestamp":"2025-06-30T11:24:27.649Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📥 Progress loaded","paramStates":1539,"instances":16}
{"timestamp":"2025-06-30T11:24:27.660Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📝 Instance registered"}
{"timestamp":"2025-06-30T11:24:27.661Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📝 Instance registered"}
{"timestamp":"2025-06-30T11:24:27.661Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ Parameter State Manager initialized","totalParams":1539,"pendingParams":1205}
{"timestamp":"2025-06-30T11:24:27.661Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ Parameter State Manager initialized","totalParams":1539,"pendingParams":1205}
{"timestamp":"2025-06-30T11:24:27.662Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🚀 正在初始化增强版Playwright爬虫"}
{"timestamp":"2025-06-30T11:24:28.439Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🌐 浏览器已启动","headless":false,"version":"138.0.7204.23"}
{"timestamp":"2025-06-30T11:24:28.440Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ 增强版Playwright爬虫初始化完成"}
{"timestamp":"2025-06-30T11:24:28.441Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ Management components initialized"}
{"timestamp":"2025-06-30T11:24:28.441Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ Health Monitor initialized","components":["tokenManager","parameterManager","crawler"]}
{"timestamp":"2025-06-30T11:24:28.442Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ Monitoring initialized"}
{"timestamp":"2025-06-30T11:24:28.442Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ Scraping Orchestrator initialized successfully"}
{"timestamp":"2025-06-30T11:24:28.442Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🎯 Starting scraping process"}
{"timestamp":"2025-06-30T11:24:28.443Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🔧 Instance count check","activeInstances":3,"maxConcurrentInstances":10,"totalInstances":17,"currentInstanceId":"instance_12999_1751282667552"}
{"timestamp":"2025-06-30T11:24:28.457Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📦 Batch assigned","batchSize":10,"remaining":1185}
{"timestamp":"2025-06-30T11:24:28.458Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📦 Processing batch","batchSize":10,"remaining":1195}
{"timestamp":"2025-06-30T11:24:28.458Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🎯 Processing parameter 1/10","param":"200_1_198_144655__"}
{"timestamp":"2025-06-30T11:24:28.459Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🚀 Starting operation: process_parameter","operation":"process_parameter","operationId":"op_1751282668459_tbgg5","phase":"start","paramKey":"200_1_198_144655__"}
{"timestamp":"2025-06-30T11:24:28.460Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🎯 开始处理参数","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"七年级上","catalogName":"第四单元"}}
{"timestamp":"2025-06-30T11:24:28.460Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📂 设置当前任务组合","combination":"初中/语文/部编版(五四制)(2024)/七年级上/第四单元","codes":{"studyPhaseCode":"200","subjectCode":"1","textbookVersionCode":"198","ceciCode":"71","catalogCode":"144655"}}
{"timestamp":"2025-06-30T11:24:28.460Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🔍 正在使用令牌[111]创建浏览器并验证..."}
{"timestamp":"2025-06-30T11:24:28.470Z","level":"WARN","instanceId":"instance_12999_1751282667552","message":"🔌 浏览器已断开连接"}
{"timestamp":"2025-06-30T11:24:28.543Z","level":"WARN","instanceId":"instance_12999_1751282667552","message":"内存使用率过高: 99.2%","type":"memory","severity":"warning","messageEn":"High memory usage: 99.2%","threshold":80,"actual":99.23,"action":"monitor_only","note":"仅监控模式 - 不会退出实例"}
{"timestamp":"2025-06-30T11:24:28.543Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📊 详细内存信息","进程堆内存已用":"22.86 MB","进程堆内存总计":"37.39 MB","进程RSS":"55.50 MB","系统内存总计":"18.00 GB","系统内存已用":"17.86 GB","系统内存空闲":"0.14 GB","系统使用率":"99.23%","进程使用率":"61.13%"}
{"timestamp":"2025-06-30T11:24:28.729Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🌐 浏览器已启动","headless":false,"version":"138.0.7204.23"}
{"timestamp":"2025-06-30T11:24:29.063Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ 已成功创建浏览器和页面并注入令牌","tokenId":111,"pageId":"page_1751282669063_njk1z","contextId":"context_1751282668730_0qb8i"}
{"timestamp":"2025-06-30T11:24:29.063Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ 浏览器实例创建成功，开始页面爬取"}
{"timestamp":"2025-06-30T11:24:29.069Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📂 输出目录已创建","encodedPath":"output/%E5%88%9D%E4%B8%AD/%E8%AF%AD%E6%96%87/%E9%83%A8%E7%BC%96%E7%89%88%28%E4%BA%94%E5%9B%9B%E5%88%B6%29%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8A/%E7%AC%AC%E5%9B%9B%E5%8D%95%E5%85%83","originalPath":"初中/语文/部编版(五四制)(2024)/七年级上/第四单元","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"七年级上","catalogName":"第四单元"}}
{"timestamp":"2025-06-30T11:24:34.462Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🔧 配置教材版本和册次设置..."}
{"timestamp":"2025-06-30T11:24:37.921Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📌 选择教材版本: 部编版(五四制)(2024)"}
{"timestamp":"2025-06-30T11:24:38.467Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📌 选择册次: 七年级上"}
{"timestamp":"2025-06-30T11:24:40.356Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📂 即将点击目录，这将触发第1页的API请求"}
{"timestamp":"2025-06-30T11:24:40.358Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📂 选择目录: 第四单元 (将触发第1页API请求)"}
{"timestamp":"2025-06-30T11:24:44.467Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ 目录选择完成: 第四单元，第1页API请求已发送"}
{"timestamp":"2025-06-30T11:24:44.469Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ 教材版本、册次和目录设置完成，第1页请求已发送"}
{"timestamp":"2025-06-30T11:24:44.489Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📄 已更新最大页数为: 1"}
{"timestamp":"2025-06-30T11:25:11.425Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ 参数处理完成","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"七年级上","catalogName":"第四单元"},"totalCount":10,"pagesProcessed":1,"duration":"42.97 seconds"}
{"timestamp":"2025-06-30T11:25:11.492Z","level":"WARN","instanceId":"instance_12999_1751282667552","message":"🔌 浏览器已断开连接"}
{"timestamp":"2025-06-30T11:25:11.578Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ Completed operation: process_parameter","operation":"process_parameter","operationId":"op_1751282668459_tbgg5","phase":"complete","paramKey":"200_1_198_144655__","dataSize":10,"pagesProcessed":1,"savedPath":"output/%E5%88%9D%E4%B8%AD/%E8%AF%AD%E6%96%87/%E9%83%A8%E7%BC%96%E7%89%88%28%E4%BA%94%E5%9B%9B%E5%88%B6%29%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8A/%E7%AC%AC%E5%9B%9B%E5%8D%95%E5%85%83"}
{"timestamp":"2025-06-30T11:25:11.588Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ Parameter completed","paramKey":"200_1_198_144655__"}
{"timestamp":"2025-06-30T11:25:11.588Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ Parameter completed","param":"200_1_198_144655__","dataSize":10,"pagesProcessed":1}
{"timestamp":"2025-06-30T11:25:11.588Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🎯 Processing parameter 2/10","param":"200_1_198_144663__"}
{"timestamp":"2025-06-30T11:25:11.589Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🚀 Starting operation: process_parameter","operation":"process_parameter","operationId":"op_1751282711589_0ocl3","phase":"start","paramKey":"200_1_198_144663__"}
{"timestamp":"2025-06-30T11:25:11.589Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🎯 开始处理参数","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"七年级上","catalogName":"第五单元 活动·探究"}}
{"timestamp":"2025-06-30T11:25:11.589Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📂 设置当前任务组合","combination":"初中/语文/部编版(五四制)(2024)/七年级上/第五单元 活动·探究","codes":{"studyPhaseCode":"200","subjectCode":"1","textbookVersionCode":"198","ceciCode":"71","catalogCode":"144663"}}
{"timestamp":"2025-06-30T11:25:11.589Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🔍 正在使用令牌[111]创建浏览器并验证..."}
{"timestamp":"2025-06-30T11:25:11.869Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🌐 浏览器已启动","headless":false,"version":"138.0.7204.23"}
{"timestamp":"2025-06-30T11:25:12.084Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ 已成功创建浏览器和页面并注入令牌","tokenId":111,"pageId":"page_1751282712084_vtt7c","contextId":"context_1751282711871_k23ju"}
{"timestamp":"2025-06-30T11:25:12.085Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ 浏览器实例创建成功，开始页面爬取"}
{"timestamp":"2025-06-30T11:25:12.085Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📂 输出目录已创建","encodedPath":"output/%E5%88%9D%E4%B8%AD/%E8%AF%AD%E6%96%87/%E9%83%A8%E7%BC%96%E7%89%88%28%E4%BA%94%E5%9B%9B%E5%88%B6%29%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8A/%E7%AC%AC%E4%BA%94%E5%8D%95%E5%85%83%20%E6%B4%BB%E5%8A%A8%C2%B7%E6%8E%A2%E7%A9%B6","originalPath":"初中/语文/部编版(五四制)(2024)/七年级上/第五单元 活动·探究","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"七年级上","catalogName":"第五单元 活动·探究"}}
{"timestamp":"2025-06-30T11:25:16.647Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🔧 配置教材版本和册次设置..."}
{"timestamp":"2025-06-30T11:25:20.069Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📌 选择教材版本: 部编版(五四制)(2024)"}
{"timestamp":"2025-06-30T11:25:20.572Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📌 选择册次: 七年级上"}
{"timestamp":"2025-06-30T11:25:22.499Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📂 即将点击目录，这将触发第1页的API请求"}
{"timestamp":"2025-06-30T11:25:22.500Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📂 选择目录: 第五单元 活动·探究 (将触发第1页API请求)"}
{"timestamp":"2025-06-30T11:25:26.573Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ 目录选择完成: 第五单元 活动·探究，第1页API请求已发送"}
{"timestamp":"2025-06-30T11:25:26.574Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ 教材版本、册次和目录设置完成，第1页请求已发送"}
{"timestamp":"2025-06-30T11:25:28.445Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📊 Status Report","parameters":{"total":1539,"completed":41,"pending":1195,"processing":27,"failed":276,"completionRate":"2.7%"},"tokens":{"total":17,"valid":17,"healthy":17},"crawler":{"successRate":"0.0%","averageResponseTime":"0ms"},"health":{"overall":"warning","alerts":1},"processing":{"totalProcessed":1,"successCount":1,"errorCount":0,"currentBatchSize":10}}
{"timestamp":"2025-06-30T11:25:34.453Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📄 已更新最大页数为: 1000"}
{"timestamp":"2025-06-30T11:26:04.455Z","level":"WARN","instanceId":"instance_12999_1751282667552","message":"⏰ 第1页响应捕获超时，跳过"}
{"timestamp":"2025-06-30T11:26:04.456Z","level":"WARN","instanceId":"instance_12999_1751282667552","message":"⏰ 第1页响应超时"}
{"timestamp":"2025-06-30T11:26:04.456Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ 参数处理完成","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"七年级上","catalogName":"第五单元 活动·探究"},"totalCount":0,"pagesProcessed":0,"duration":"52.87 seconds"}
{"timestamp":"2025-06-30T11:26:04.509Z","level":"WARN","instanceId":"instance_12999_1751282667552","message":"🔌 浏览器已断开连接"}
{"timestamp":"2025-06-30T11:26:04.582Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ Completed operation: process_parameter","operation":"process_parameter","operationId":"op_1751282711589_0ocl3","phase":"complete","paramKey":"200_1_198_144663__","dataSize":0,"pagesProcessed":0,"savedPath":"output/%E5%88%9D%E4%B8%AD/%E8%AF%AD%E6%96%87/%E9%83%A8%E7%BC%96%E7%89%88%28%E4%BA%94%E5%9B%9B%E5%88%B6%29%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8A/%E7%AC%AC%E4%BA%94%E5%8D%95%E5%85%83%20%E6%B4%BB%E5%8A%A8%C2%B7%E6%8E%A2%E7%A9%B6"}
{"timestamp":"2025-06-30T11:26:04.589Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ Parameter completed","paramKey":"200_1_198_144663__"}
{"timestamp":"2025-06-30T11:26:04.590Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ Parameter completed","param":"200_1_198_144663__","dataSize":0,"pagesProcessed":0}
{"timestamp":"2025-06-30T11:26:04.590Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🎯 Processing parameter 3/10","param":"200_1_198_144671__"}
{"timestamp":"2025-06-30T11:26:04.590Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🚀 Starting operation: process_parameter","operation":"process_parameter","operationId":"op_1751282764590_fygqx","phase":"start","paramKey":"200_1_198_144671__"}
{"timestamp":"2025-06-30T11:26:04.590Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🎯 开始处理参数","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"七年级上","catalogName":"第六单元"}}
{"timestamp":"2025-06-30T11:26:04.590Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📂 设置当前任务组合","combination":"初中/语文/部编版(五四制)(2024)/七年级上/第六单元","codes":{"studyPhaseCode":"200","subjectCode":"1","textbookVersionCode":"198","ceciCode":"71","catalogCode":"144671"}}
{"timestamp":"2025-06-30T11:26:04.590Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🔍 正在使用令牌[111]创建浏览器并验证..."}
{"timestamp":"2025-06-30T11:26:04.875Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🌐 浏览器已启动","headless":false,"version":"138.0.7204.23"}
{"timestamp":"2025-06-30T11:26:05.118Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ 已成功创建浏览器和页面并注入令牌","tokenId":111,"pageId":"page_1751282765118_lpqxv","contextId":"context_1751282764876_7tivn"}
{"timestamp":"2025-06-30T11:26:05.118Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ 浏览器实例创建成功，开始页面爬取"}
{"timestamp":"2025-06-30T11:26:05.118Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📂 输出目录已创建","encodedPath":"output/%E5%88%9D%E4%B8%AD/%E8%AF%AD%E6%96%87/%E9%83%A8%E7%BC%96%E7%89%88%28%E4%BA%94%E5%9B%9B%E5%88%B6%29%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8A/%E7%AC%AC%E5%85%AD%E5%8D%95%E5%85%83","originalPath":"初中/语文/部编版(五四制)(2024)/七年级上/第六单元","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"七年级上","catalogName":"第六单元"}}
{"timestamp":"2025-06-30T11:26:07.120Z","level":"WARN","instanceId":"instance_12999_1751282667552","message":"❌ 第1次导航尝试失败","url":"https://zj.stzy.com/create-paper/chapter?studyPhaseCode=200&subjectCode=1&textbookVersionCode=198&catalogCode=144671","error":"page.goto: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - navigating to \"https://zj.stzy.com/create-paper/chapter?studyPhaseCode=200&subjectCode=1&textbookVersionCode=198&catalogCode=144671\", waiting until \"networkidle\"\u001b[22m\n"}
{"timestamp":"2025-06-30T11:26:12.122Z","level":"WARN","instanceId":"instance_12999_1751282667552","message":"❌ 第2次导航尝试失败","url":"https://zj.stzy.com/create-paper/chapter?studyPhaseCode=200&subjectCode=1&textbookVersionCode=198&catalogCode=144671","error":"page.goto: Target page, context or browser has been closed"}
{"timestamp":"2025-06-30T11:26:22.126Z","level":"WARN","instanceId":"instance_12999_1751282667552","message":"❌ 第3次导航尝试失败","url":"https://zj.stzy.com/create-paper/chapter?studyPhaseCode=200&subjectCode=1&textbookVersionCode=198&catalogCode=144671","error":"page.goto: Target page, context or browser has been closed"}
{"timestamp":"2025-06-30T11:26:22.128Z","level":"ERROR","instanceId":"instance_12999_1751282667552","message":"❌ 处理第1页时出错","error":"导航在3次尝试后失败: page.goto: Target page, context or browser has been closed","parameters":{"studyPhaseCode":"200","studyPhaseName":"初中","subjectCode":"1","subjectName":"语文","textbookVersionCode":"198","textbookVersionName":"部编版(五四制)(2024)","ceciCode":"71","ceciName":"七年级上","gradeCode":"7","catalogCode":"144671","catalogName":"第六单元","key":"200_1_198_144671__","id":332}}
{"timestamp":"2025-06-30T11:26:22.128Z","level":"ERROR","instanceId":"instance_12999_1751282667552","message":"❌ 参数处理失败","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"七年级上","catalogName":"第六单元"},"error":"导航在3次尝试后失败: page.goto: Target page, context or browser has been closed","duration":"17.54 seconds"}
{"timestamp":"2025-06-30T11:26:22.138Z","level":"WARN","instanceId":"instance_12999_1751282667552","message":"🔌 浏览器已断开连接"}
{"timestamp":"2025-06-30T11:26:22.214Z","level":"ERROR","instanceId":"instance_12999_1751282667552","message":"❌ Failed operation: process_parameter","operation":"process_parameter","operationId":"op_1751282764590_fygqx","phase":"failed","error":"导航在3次尝试后失败: page.goto: Target page, context or browser has been closed","paramKey":"200_1_198_144671__"}
{"timestamp":"2025-06-30T11:26:22.220Z","level":"WARN","instanceId":"instance_12999_1751282667552","message":"❌ Parameter failed","paramKey":"200_1_198_144671__","error":"导航在3次尝试后失败: page.goto: Target page, context or browser has been closed","attempts":1}
{"timestamp":"2025-06-30T11:26:22.220Z","level":"ERROR","instanceId":"instance_12999_1751282667552","message":"❌ Parameter failed","param":"200_1_198_144671__","error":"导航在3次尝试后失败: page.goto: Target page, context or browser has been closed"}
{"timestamp":"2025-06-30T11:26:22.220Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🎯 Processing parameter 4/10","param":"200_1_198_149953__"}
{"timestamp":"2025-06-30T11:26:22.220Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🚀 Starting operation: process_parameter","operation":"process_parameter","operationId":"op_1751282782220_4y78l","phase":"start","paramKey":"200_1_198_149953__"}
{"timestamp":"2025-06-30T11:26:22.220Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🎯 开始处理参数","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"七年级下","catalogName":"第一单元"}}
{"timestamp":"2025-06-30T11:26:22.220Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📂 设置当前任务组合","combination":"初中/语文/部编版(五四制)(2024)/七年级下/第一单元","codes":{"studyPhaseCode":"200","subjectCode":"1","textbookVersionCode":"198","ceciCode":"72","catalogCode":"149953"}}
{"timestamp":"2025-06-30T11:26:22.220Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🔍 正在使用令牌[111]创建浏览器并验证..."}
{"timestamp":"2025-06-30T11:26:22.389Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🌐 浏览器已启动","headless":false,"version":"138.0.7204.23"}
{"timestamp":"2025-06-30T11:26:22.745Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ 已成功创建浏览器和页面并注入令牌","tokenId":111,"pageId":"page_1751282782745_bx65g","contextId":"context_1751282782390_wc57x"}
{"timestamp":"2025-06-30T11:26:22.745Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ 浏览器实例创建成功，开始页面爬取"}
{"timestamp":"2025-06-30T11:26:22.745Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📂 输出目录已创建","encodedPath":"output/%E5%88%9D%E4%B8%AD/%E8%AF%AD%E6%96%87/%E9%83%A8%E7%BC%96%E7%89%88%28%E4%BA%94%E5%9B%9B%E5%88%B6%29%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E4%B8%80%E5%8D%95%E5%85%83","originalPath":"初中/语文/部编版(五四制)(2024)/七年级下/第一单元","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"七年级下","catalogName":"第一单元"}}
{"timestamp":"2025-06-30T11:26:27.731Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🔧 配置教材版本和册次设置..."}
{"timestamp":"2025-06-30T11:26:28.444Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📊 Status Report","parameters":{"total":1539,"completed":42,"pending":1195,"processing":25,"failed":276,"completionRate":"2.7%"},"tokens":{"total":17,"valid":17,"healthy":17},"crawler":{"successRate":"0.0%","averageResponseTime":"0ms"},"health":{"overall":"warning","alerts":1},"processing":{"totalProcessed":3,"successCount":2,"errorCount":1,"currentBatchSize":10}}
{"timestamp":"2025-06-30T11:26:31.164Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📌 选择教材版本: 部编版(五四制)(2024)"}
{"timestamp":"2025-06-30T11:26:31.678Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📌 选择册次: 七年级下"}
{"timestamp":"2025-06-30T11:26:33.623Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📂 即将点击目录，这将触发第1页的API请求"}
{"timestamp":"2025-06-30T11:26:33.623Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📂 选择目录: 第一单元 (将触发第1页API请求)"}
{"timestamp":"2025-06-30T11:26:37.674Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ 目录选择完成: 第一单元，第1页API请求已发送"}
{"timestamp":"2025-06-30T11:26:37.675Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ 教材版本、册次和目录设置完成，第1页请求已发送"}
{"timestamp":"2025-06-30T11:27:07.679Z","level":"WARN","instanceId":"instance_12999_1751282667552","message":"⚠️ 从分页获取最大页数失败: locator.waitFor: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('ul.ant-pagination') to be visible\u001b[22m\n\u001b[2m    64 × locator resolved to hidden <ul data-v-46b589e4=\"\" unselectable=\"unselectable\" class=\"ml_30 mr_30 ant-pagination\">…</ul>\u001b[22m\n"}
{"timestamp":"2025-06-30T11:27:07.680Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📄 已更新最大页数为: 1"}
{"timestamp":"2025-06-30T11:27:09.723Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"没有查询到您想要的内容~","reason":"显示无内容消息"}
{"timestamp":"2025-06-30T11:27:09.724Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ 参数处理完成","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"七年级下","catalogName":"第一单元"},"totalCount":0,"pagesProcessed":0,"duration":"47.50 seconds"}
{"timestamp":"2025-06-30T11:27:09.756Z","level":"WARN","instanceId":"instance_12999_1751282667552","message":"🔌 浏览器已断开连接"}
{"timestamp":"2025-06-30T11:27:09.832Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ Completed operation: process_parameter","operation":"process_parameter","operationId":"op_1751282782220_4y78l","phase":"complete","paramKey":"200_1_198_149953__","dataSize":0,"pagesProcessed":0,"savedPath":"output/%E5%88%9D%E4%B8%AD/%E8%AF%AD%E6%96%87/%E9%83%A8%E7%BC%96%E7%89%88%28%E4%BA%94%E5%9B%9B%E5%88%B6%29%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E4%B8%80%E5%8D%95%E5%85%83"}
{"timestamp":"2025-06-30T11:27:09.839Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ Parameter completed","paramKey":"200_1_198_149953__"}
{"timestamp":"2025-06-30T11:27:09.839Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ Parameter completed","param":"200_1_198_149953__","dataSize":0,"pagesProcessed":0}
{"timestamp":"2025-06-30T11:27:09.839Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🎯 Processing parameter 5/10","param":"200_1_198_149960__"}
{"timestamp":"2025-06-30T11:27:09.839Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🚀 Starting operation: process_parameter","operation":"process_parameter","operationId":"op_1751282829839_4tbz1","phase":"start","paramKey":"200_1_198_149960__"}
{"timestamp":"2025-06-30T11:27:09.839Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🎯 开始处理参数","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"七年级下","catalogName":"第二单元"}}
{"timestamp":"2025-06-30T11:27:09.839Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📂 设置当前任务组合","combination":"初中/语文/部编版(五四制)(2024)/七年级下/第二单元","codes":{"studyPhaseCode":"200","subjectCode":"1","textbookVersionCode":"198","ceciCode":"72","catalogCode":"149960"}}
{"timestamp":"2025-06-30T11:27:09.839Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🔍 正在使用令牌[111]创建浏览器并验证..."}
{"timestamp":"2025-06-30T11:27:10.125Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🌐 浏览器已启动","headless":false,"version":"138.0.7204.23"}
{"timestamp":"2025-06-30T11:27:10.363Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ 已成功创建浏览器和页面并注入令牌","tokenId":111,"pageId":"page_1751282830363_8a3e6","contextId":"context_1751282830126_cwexl"}
{"timestamp":"2025-06-30T11:27:10.363Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ 浏览器实例创建成功，开始页面爬取"}
{"timestamp":"2025-06-30T11:27:10.363Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📂 输出目录已创建","encodedPath":"output/%E5%88%9D%E4%B8%AD/%E8%AF%AD%E6%96%87/%E9%83%A8%E7%BC%96%E7%89%88%28%E4%BA%94%E5%9B%9B%E5%88%B6%29%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E4%BA%8C%E5%8D%95%E5%85%83","originalPath":"初中/语文/部编版(五四制)(2024)/七年级下/第二单元","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"七年级下","catalogName":"第二单元"}}
{"timestamp":"2025-06-30T11:27:15.391Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🔧 配置教材版本和册次设置..."}
{"timestamp":"2025-06-30T11:27:18.827Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📌 选择教材版本: 部编版(五四制)(2024)"}
{"timestamp":"2025-06-30T11:27:19.392Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📌 选择册次: 七年级下"}
{"timestamp":"2025-06-30T11:27:21.271Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📂 即将点击目录，这将触发第1页的API请求"}
{"timestamp":"2025-06-30T11:27:21.272Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📂 选择目录: 第二单元 (将触发第1页API请求)"}
{"timestamp":"2025-06-30T11:27:25.337Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ 目录选择完成: 第二单元，第1页API请求已发送"}
{"timestamp":"2025-06-30T11:27:25.338Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ 教材版本、册次和目录设置完成，第1页请求已发送"}
{"timestamp":"2025-06-30T11:27:28.446Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📊 Status Report","parameters":{"total":1539,"completed":43,"pending":1195,"processing":24,"failed":276,"completionRate":"2.8%"},"tokens":{"total":17,"valid":17,"healthy":17},"crawler":{"successRate":"0.0%","averageResponseTime":"0ms"},"health":{"overall":"warning","alerts":1},"processing":{"totalProcessed":4,"successCount":3,"errorCount":1,"currentBatchSize":10}}
{"timestamp":"2025-06-30T11:27:37.685Z","level":"WARN","instanceId":"instance_12999_1751282667552","message":"⏰ 第1页响应捕获超时，跳过"}
{"timestamp":"2025-06-30T11:27:55.341Z","level":"WARN","instanceId":"instance_12999_1751282667552","message":"⚠️ 从分页获取最大页数失败: locator.waitFor: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('ul.ant-pagination') to be visible\u001b[22m\n\u001b[2m    64 × locator resolved to hidden <ul data-v-46b589e4=\"\" unselectable=\"unselectable\" class=\"ml_30 mr_30 ant-pagination\">…</ul>\u001b[22m\n"}
{"timestamp":"2025-06-30T11:27:55.342Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📄 已更新最大页数为: 1"}
{"timestamp":"2025-06-30T11:27:57.384Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"没有查询到您想要的内容~","reason":"显示无内容消息"}
{"timestamp":"2025-06-30T11:27:57.385Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ 参数处理完成","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"七年级下","catalogName":"第二单元"},"totalCount":0,"pagesProcessed":0,"duration":"47.55 seconds"}
{"timestamp":"2025-06-30T11:27:57.409Z","level":"WARN","instanceId":"instance_12999_1751282667552","message":"🔌 浏览器已断开连接"}
{"timestamp":"2025-06-30T11:27:57.475Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ Completed operation: process_parameter","operation":"process_parameter","operationId":"op_1751282829839_4tbz1","phase":"complete","paramKey":"200_1_198_149960__","dataSize":0,"pagesProcessed":0,"savedPath":"output/%E5%88%9D%E4%B8%AD/%E8%AF%AD%E6%96%87/%E9%83%A8%E7%BC%96%E7%89%88%28%E4%BA%94%E5%9B%9B%E5%88%B6%29%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E4%BA%8C%E5%8D%95%E5%85%83"}
{"timestamp":"2025-06-30T11:27:57.481Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ Parameter completed","paramKey":"200_1_198_149960__"}
{"timestamp":"2025-06-30T11:27:57.481Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ Parameter completed","param":"200_1_198_149960__","dataSize":0,"pagesProcessed":0}
{"timestamp":"2025-06-30T11:27:57.482Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🎯 Processing parameter 6/10","param":"200_1_198_149969__"}
{"timestamp":"2025-06-30T11:27:57.482Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🚀 Starting operation: process_parameter","operation":"process_parameter","operationId":"op_1751282877482_jsftz","phase":"start","paramKey":"200_1_198_149969__"}
{"timestamp":"2025-06-30T11:27:57.482Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🎯 开始处理参数","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"七年级下","catalogName":"第三单元"}}
{"timestamp":"2025-06-30T11:27:57.482Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📂 设置当前任务组合","combination":"初中/语文/部编版(五四制)(2024)/七年级下/第三单元","codes":{"studyPhaseCode":"200","subjectCode":"1","textbookVersionCode":"198","ceciCode":"72","catalogCode":"149969"}}
{"timestamp":"2025-06-30T11:27:57.482Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🔍 正在使用令牌[111]创建浏览器并验证..."}
{"timestamp":"2025-06-30T11:27:57.742Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🌐 浏览器已启动","headless":false,"version":"138.0.7204.23"}
{"timestamp":"2025-06-30T11:27:58.010Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ 已成功创建浏览器和页面并注入令牌","tokenId":111,"pageId":"page_1751282878010_cvgo1","contextId":"context_1751282877743_7koeo"}
{"timestamp":"2025-06-30T11:27:58.010Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ 浏览器实例创建成功，开始页面爬取"}
{"timestamp":"2025-06-30T11:27:58.010Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📂 输出目录已创建","encodedPath":"output/%E5%88%9D%E4%B8%AD/%E8%AF%AD%E6%96%87/%E9%83%A8%E7%BC%96%E7%89%88%28%E4%BA%94%E5%9B%9B%E5%88%B6%29%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E4%B8%89%E5%8D%95%E5%85%83","originalPath":"初中/语文/部编版(五四制)(2024)/七年级下/第三单元","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"七年级下","catalogName":"第三单元"}}
{"timestamp":"2025-06-30T11:28:02.932Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🔧 配置教材版本和册次设置..."}
{"timestamp":"2025-06-30T11:28:06.356Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📌 选择教材版本: 部编版(五四制)(2024)"}
{"timestamp":"2025-06-30T11:28:06.860Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📌 选择册次: 七年级下"}
{"timestamp":"2025-06-30T11:28:08.745Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📂 即将点击目录，这将触发第1页的API请求"}
{"timestamp":"2025-06-30T11:28:08.745Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📂 选择目录: 第三单元 (将触发第1页API请求)"}
{"timestamp":"2025-06-30T11:28:12.818Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ 目录选择完成: 第三单元，第1页API请求已发送"}
{"timestamp":"2025-06-30T11:28:12.818Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ 教材版本、册次和目录设置完成，第1页请求已发送"}
{"timestamp":"2025-06-30T11:28:12.824Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📄 已更新最大页数为: 1"}
{"timestamp":"2025-06-30T11:28:25.346Z","level":"WARN","instanceId":"instance_12999_1751282667552","message":"⏰ 第1页响应捕获超时，跳过"}
{"timestamp":"2025-06-30T11:28:27.662Z","level":"WARN","instanceId":"instance_76459_1751281094768","message":"🧹 Cleaned up stale instance","cleanedParams":10}
{"timestamp":"2025-06-30T11:28:27.663Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🧹 Cleanup completed","cleanedParams":10,"processedInstances":1}
{"timestamp":"2025-06-30T11:28:28.445Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📊 Status Report","parameters":{"total":1539,"completed":44,"pending":1205,"processing":13,"failed":276,"completionRate":"2.9%"},"tokens":{"total":17,"valid":17,"healthy":17},"crawler":{"successRate":"0.0%","averageResponseTime":"0ms"},"health":{"overall":"warning","alerts":1},"processing":{"totalProcessed":5,"successCount":4,"errorCount":1,"currentBatchSize":10}}
{"timestamp":"2025-06-30T11:28:42.826Z","level":"WARN","instanceId":"instance_12999_1751282667552","message":"⏰ 第1页响应捕获超时，跳过"}
{"timestamp":"2025-06-30T11:28:42.827Z","level":"WARN","instanceId":"instance_12999_1751282667552","message":"⏰ 第1页响应超时"}
{"timestamp":"2025-06-30T11:28:42.827Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ 参数处理完成","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"七年级下","catalogName":"第三单元"},"totalCount":0,"pagesProcessed":0,"duration":"45.34 seconds"}
{"timestamp":"2025-06-30T11:28:42.895Z","level":"WARN","instanceId":"instance_12999_1751282667552","message":"🔌 浏览器已断开连接"}
{"timestamp":"2025-06-30T11:28:42.990Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ Completed operation: process_parameter","operation":"process_parameter","operationId":"op_1751282877482_jsftz","phase":"complete","paramKey":"200_1_198_149969__","dataSize":0,"pagesProcessed":0,"savedPath":"output/%E5%88%9D%E4%B8%AD/%E8%AF%AD%E6%96%87/%E9%83%A8%E7%BC%96%E7%89%88%28%E4%BA%94%E5%9B%9B%E5%88%B6%29%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E4%B8%89%E5%8D%95%E5%85%83"}
{"timestamp":"2025-06-30T11:28:42.999Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ Parameter completed","paramKey":"200_1_198_149969__"}
{"timestamp":"2025-06-30T11:28:42.999Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ Parameter completed","param":"200_1_198_149969__","dataSize":0,"pagesProcessed":0}
{"timestamp":"2025-06-30T11:28:42.999Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🎯 Processing parameter 7/10","param":"200_1_198_149982__"}
{"timestamp":"2025-06-30T11:28:42.999Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🚀 Starting operation: process_parameter","operation":"process_parameter","operationId":"op_1751282922999_2uwow","phase":"start","paramKey":"200_1_198_149982__"}
{"timestamp":"2025-06-30T11:28:42.999Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🎯 开始处理参数","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"七年级下","catalogName":"第四单元"}}
{"timestamp":"2025-06-30T11:28:42.999Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📂 设置当前任务组合","combination":"初中/语文/部编版(五四制)(2024)/七年级下/第四单元","codes":{"studyPhaseCode":"200","subjectCode":"1","textbookVersionCode":"198","ceciCode":"72","catalogCode":"149982"}}
{"timestamp":"2025-06-30T11:28:42.999Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🔍 正在使用令牌[111]创建浏览器并验证..."}
{"timestamp":"2025-06-30T11:28:43.298Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🌐 浏览器已启动","headless":false,"version":"138.0.7204.23"}
{"timestamp":"2025-06-30T11:28:43.531Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ 已成功创建浏览器和页面并注入令牌","tokenId":111,"pageId":"page_1751282923531_250u5","contextId":"context_1751282923299_tpe21"}
{"timestamp":"2025-06-30T11:28:43.532Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"✅ 浏览器实例创建成功，开始页面爬取"}
{"timestamp":"2025-06-30T11:28:43.533Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📂 输出目录已创建","encodedPath":"output/%E5%88%9D%E4%B8%AD/%E8%AF%AD%E6%96%87/%E9%83%A8%E7%BC%96%E7%89%88%28%E4%BA%94%E5%9B%9B%E5%88%B6%29%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%9B%9B%E5%8D%95%E5%85%83","originalPath":"初中/语文/部编版(五四制)(2024)/七年级下/第四单元","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"七年级下","catalogName":"第四单元"}}
{"timestamp":"2025-06-30T11:28:48.366Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🔧 配置教材版本和册次设置..."}
{"timestamp":"2025-06-30T11:28:48.369Z","level":"ERROR","instanceId":"instance_12999_1751282667552","message":"❌ 设置配置失败","error":"page.waitForTimeout: Target page, context or browser has been closed"}
{"timestamp":"2025-06-30T11:28:48.370Z","level":"ERROR","instanceId":"instance_12999_1751282667552","message":"❌ 处理第1页时出错","error":"page.waitForTimeout: Target page, context or browser has been closed","parameters":{"studyPhaseCode":"200","studyPhaseName":"初中","subjectCode":"1","subjectName":"语文","textbookVersionCode":"198","textbookVersionName":"部编版(五四制)(2024)","ceciCode":"72","ceciName":"七年级下","gradeCode":"7","catalogCode":"149982","catalogName":"第四单元","key":"200_1_198_149982__","id":336}}
{"timestamp":"2025-06-30T11:28:48.371Z","level":"ERROR","instanceId":"instance_12999_1751282667552","message":"❌ 参数处理失败","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"七年级下","catalogName":"第四单元"},"error":"page.waitForTimeout: Target page, context or browser has been closed","duration":"5.37 seconds"}
{"timestamp":"2025-06-30T11:28:48.383Z","level":"WARN","instanceId":"instance_12999_1751282667552","message":"🔌 浏览器已断开连接"}
{"timestamp":"2025-06-30T11:28:48.470Z","level":"ERROR","instanceId":"instance_12999_1751282667552","message":"❌ Failed operation: process_parameter","operation":"process_parameter","operationId":"op_1751282922999_2uwow","phase":"failed","error":"page.waitForTimeout: Target page, context or browser has been closed","paramKey":"200_1_198_149982__"}
{"timestamp":"2025-06-30T11:28:48.476Z","level":"WARN","instanceId":"instance_12999_1751282667552","message":"❌ Parameter failed","paramKey":"200_1_198_149982__","error":"page.waitForTimeout: Target page, context or browser has been closed","attempts":1}
{"timestamp":"2025-06-30T11:28:48.476Z","level":"ERROR","instanceId":"instance_12999_1751282667552","message":"❌ Parameter failed","param":"200_1_198_149982__","error":"page.waitForTimeout: Target page, context or browser has been closed"}
{"timestamp":"2025-06-30T11:28:48.476Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🎯 Processing parameter 8/10","param":"200_1_177_133451__"}
{"timestamp":"2025-06-30T11:28:48.476Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🚀 Starting operation: process_parameter","operation":"process_parameter","operationId":"op_1751282928476_0nqqo","phase":"start","paramKey":"200_1_177_133451__"}
{"timestamp":"2025-06-30T11:28:48.476Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🎯 开始处理参数","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版（五四制）","ceciName":"六年级上","catalogName":"第四单元"}}
{"timestamp":"2025-06-30T11:28:48.476Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📂 设置当前任务组合","combination":"初中/语文/部编版（五四制）/六年级上/第四单元","codes":{"studyPhaseCode":"200","subjectCode":"1","textbookVersionCode":"177","ceciCode":"172","catalogCode":"133451"}}
{"timestamp":"2025-06-30T11:28:48.477Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🔍 正在使用令牌[111]创建浏览器并验证..."}
{"timestamp":"2025-06-30T11:28:48.507Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🔄 Logger shutting down..."}
{"timestamp":"2025-06-30T11:28:48.507Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🔄 Received SIGINT, initiating graceful shutdown..."}
{"timestamp":"2025-06-30T11:28:48.507Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🔄 Shutting down Scraping Orchestrator..."}
{"timestamp":"2025-06-30T11:28:48.507Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🛑 Stopping scraping process..."}
{"timestamp":"2025-06-30T11:28:48.659Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🌐 浏览器已启动","headless":false,"version":"138.0.7204.23"}
{"timestamp":"2025-06-30T11:28:48.663Z","level":"WARN","instanceId":"instance_12999_1751282667552","message":"🔌 浏览器已断开连接"}
{"timestamp":"2025-06-30T11:28:48.665Z","level":"ERROR","instanceId":"instance_12999_1751282667552","message":"❌ 创建浏览器和令牌失败","error":"browser.newContext: Target page, context or browser has been closed"}
{"timestamp":"2025-06-30T11:28:48.665Z","level":"ERROR","instanceId":"instance_12999_1751282667552","message":"❌ 参数处理失败","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版（五四制）","ceciName":"六年级上","catalogName":"第四单元"},"error":"browser.newContext: Target page, context or browser has been closed","duration":"0.19 seconds"}
{"timestamp":"2025-06-30T11:28:48.665Z","level":"ERROR","instanceId":"instance_12999_1751282667552","message":"❌ Failed operation: process_parameter","operation":"process_parameter","operationId":"op_1751282928476_0nqqo","phase":"failed","error":"browser.newContext: Target page, context or browser has been closed","paramKey":"200_1_177_133451__"}
{"timestamp":"2025-06-30T11:28:48.672Z","level":"WARN","instanceId":"instance_12999_1751282667552","message":"❌ Parameter failed","paramKey":"200_1_177_133451__","error":"browser.newContext: Target page, context or browser has been closed","attempts":1}
{"timestamp":"2025-06-30T11:28:48.672Z","level":"ERROR","instanceId":"instance_12999_1751282667552","message":"❌ Parameter failed","param":"200_1_177_133451__","error":"browser.newContext: Target page, context or browser has been closed"}
{"timestamp":"2025-06-30T11:28:48.672Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🎯 Processing parameter 9/10","param":"200_1_177_133458__"}
{"timestamp":"2025-06-30T11:28:48.672Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🚀 Starting operation: process_parameter","operation":"process_parameter","operationId":"op_1751282928672_b5ayv","phase":"start","paramKey":"200_1_177_133458__"}
{"timestamp":"2025-06-30T11:28:48.672Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🎯 开始处理参数","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版（五四制）","ceciName":"六年级上","catalogName":"第五单元"}}
{"timestamp":"2025-06-30T11:28:48.672Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"📂 设置当前任务组合","combination":"初中/语文/部编版（五四制）/六年级上/第五单元","codes":{"studyPhaseCode":"200","subjectCode":"1","textbookVersionCode":"177","ceciCode":"172","catalogCode":"133458"}}
{"timestamp":"2025-06-30T11:28:48.673Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🔍 正在使用令牌[111]创建浏览器并验证..."}
{"timestamp":"2025-06-30T11:28:48.718Z","level":"INFO","instanceId":"instance_12999_1751282667552","message":"🔄 Logger shutting down..."}
{"timestamp":"2025-06-30T11:35:11.730Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ Core components initialized"}
{"timestamp":"2025-06-30T11:35:11.752Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🔧 TokenLifecycleManager configuration","environment":"development","ignoreTokenExpiration":true,"tokensFile":"./tokens.json"}
{"timestamp":"2025-06-30T11:35:11.753Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📥 Tokens loaded successfully","totalTokens":17,"validTokens":17}
{"timestamp":"2025-06-30T11:35:11.754Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🔄 Startup token validation completed","markedValid":17,"total":17,"note":"All tokens with valid JWT structure marked as valid at startup"}
{"timestamp":"2025-06-30T11:35:11.754Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ Token Lifecycle Manager initialized","totalTokens":17,"validTokens":17,"note":"All tokens marked as valid at startup"}
{"timestamp":"2025-06-30T11:35:11.805Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📥 Tokens loaded successfully","totalTokens":17,"validTokens":17}
{"timestamp":"2025-06-30T11:35:11.806Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🔄 Startup token validation completed","markedValid":17,"total":17,"note":"All tokens with valid JWT structure marked as valid at startup"}
{"timestamp":"2025-06-30T11:35:11.806Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ Token Lifecycle Manager initialized","totalTokens":17,"validTokens":17,"note":"All tokens marked as valid at startup"}
{"timestamp":"2025-06-30T11:35:11.810Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📥 Parameters loaded","count":1539}
{"timestamp":"2025-06-30T11:35:11.812Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📥 Parameters loaded","count":1539}
{"timestamp":"2025-06-30T11:35:11.820Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📥 Progress loaded","paramStates":1539,"instances":17}
{"timestamp":"2025-06-30T11:35:11.826Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📥 Progress loaded","paramStates":1539,"instances":17}
{"timestamp":"2025-06-30T11:35:11.838Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📝 Instance registered"}
{"timestamp":"2025-06-30T11:35:11.838Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📝 Instance registered"}
{"timestamp":"2025-06-30T11:35:11.839Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ Parameter State Manager initialized","totalParams":1539,"pendingParams":1208}
{"timestamp":"2025-06-30T11:35:11.839Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ Parameter State Manager initialized","totalParams":1539,"pendingParams":1208}
{"timestamp":"2025-06-30T11:35:11.839Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🚀 正在初始化增强版Playwright爬虫"}
{"timestamp":"2025-06-30T11:35:12.396Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🌐 浏览器已启动","headless":false,"version":"138.0.7204.23"}
{"timestamp":"2025-06-30T11:35:12.396Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ 增强版Playwright爬虫初始化完成"}
{"timestamp":"2025-06-30T11:35:12.396Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ Management components initialized"}
{"timestamp":"2025-06-30T11:35:12.397Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ Health Monitor initialized","components":["tokenManager","parameterManager","crawler"]}
{"timestamp":"2025-06-30T11:35:12.397Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ Monitoring initialized"}
{"timestamp":"2025-06-30T11:35:12.397Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ Scraping Orchestrator initialized successfully"}
{"timestamp":"2025-06-30T11:35:12.397Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🎯 Starting scraping process"}
{"timestamp":"2025-06-30T11:35:12.397Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🔧 Instance count check","activeInstances":3,"maxConcurrentInstances":10,"totalInstances":18,"currentInstanceId":"instance_15850_1751283311730"}
{"timestamp":"2025-06-30T11:35:12.403Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📦 Batch assigned","batchSize":10,"remaining":1188}
{"timestamp":"2025-06-30T11:35:12.404Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📦 Processing batch","batchSize":10,"remaining":1198}
{"timestamp":"2025-06-30T11:35:12.404Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🎯 Processing parameter 1/10","param":"200_1_198_144605__"}
{"timestamp":"2025-06-30T11:35:12.404Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🚀 Starting operation: process_parameter","operation":"process_parameter","operationId":"op_1751283312404_517t5","phase":"start","paramKey":"200_1_198_144605__"}
{"timestamp":"2025-06-30T11:35:12.404Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🎯 开始处理参数","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"六年级上","catalogName":"第七单元 "}}
{"timestamp":"2025-06-30T11:35:12.404Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📂 设置当前任务组合","combination":"初中/语文/部编版(五四制)(2024)/六年级上/第七单元 ","codes":{"studyPhaseCode":"200","subjectCode":"1","textbookVersionCode":"198","ceciCode":"172","catalogCode":"144605"}}
{"timestamp":"2025-06-30T11:35:12.405Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🔍 正在使用令牌[111]创建浏览器并验证..."}
{"timestamp":"2025-06-30T11:35:12.419Z","level":"WARN","instanceId":"instance_15850_1751283311730","message":"🔌 浏览器已断开连接"}
{"timestamp":"2025-06-30T11:35:12.498Z","level":"WARN","instanceId":"instance_15850_1751283311730","message":"内存使用率过高: 99.4%","type":"memory","severity":"warning","messageEn":"High memory usage: 99.4%","threshold":80,"actual":99.37,"action":"monitor_only","note":"仅监控模式 - 不会退出实例"}
{"timestamp":"2025-06-30T11:35:12.499Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📊 详细内存信息","进程堆内存已用":"23.08 MB","进程堆内存总计":"37.45 MB","进程RSS":"94.67 MB","系统内存总计":"18.00 GB","系统内存已用":"17.89 GB","系统内存空闲":"0.11 GB","系统使用率":"99.37%","进程使用率":"61.61%"}
{"timestamp":"2025-06-30T11:35:12.652Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🌐 浏览器已启动","headless":false,"version":"138.0.7204.23"}
{"timestamp":"2025-06-30T11:35:12.950Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ 已成功创建浏览器和页面并注入令牌","tokenId":111,"pageId":"page_1751283312950_lgjnc","contextId":"context_1751283312653_8atii"}
{"timestamp":"2025-06-30T11:35:12.950Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ 浏览器实例创建成功，开始页面爬取"}
{"timestamp":"2025-06-30T11:35:12.951Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📂 输出目录已创建","encodedPath":"output/%E5%88%9D%E4%B8%AD/%E8%AF%AD%E6%96%87/%E9%83%A8%E7%BC%96%E7%89%88%28%E4%BA%94%E5%9B%9B%E5%88%B6%29%282024%29/%E5%85%AD%E5%B9%B4%E7%BA%A7%E4%B8%8A/%E7%AC%AC%E4%B8%83%E5%8D%95%E5%85%83%20","originalPath":"初中/语文/部编版(五四制)(2024)/六年级上/第七单元 ","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"六年级上","catalogName":"第七单元 "}}
{"timestamp":"2025-06-30T11:35:17.756Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🔧 配置教材版本和册次设置..."}
{"timestamp":"2025-06-30T11:35:21.211Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📌 选择教材版本: 部编版(五四制)(2024)"}
{"timestamp":"2025-06-30T11:35:21.737Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📌 选择册次: 六年级上"}
{"timestamp":"2025-06-30T11:35:23.666Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🔧 设置持续网络监听以捕获所有API请求"}
{"timestamp":"2025-06-30T11:35:23.667Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ 持续网络监听已设置完成"}
{"timestamp":"2025-06-30T11:35:23.667Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📂 即将点击目录，这将触发第1页的API请求"}
{"timestamp":"2025-06-30T11:35:23.668Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📂 选择目录: 第七单元  (将触发第1页API请求)"}
{"timestamp":"2025-06-30T11:35:25.731Z","level":"ERROR","instanceId":"instance_15850_1751283311730","message":"❌ 目录选择失败","error":"未找到目录: 第七单元 "}
{"timestamp":"2025-06-30T11:35:25.731Z","level":"ERROR","instanceId":"instance_15850_1751283311730","message":"❌ 设置配置失败","error":"未找到目录: 第七单元 "}
{"timestamp":"2025-06-30T11:35:25.731Z","level":"ERROR","instanceId":"instance_15850_1751283311730","message":"❌ 处理第1页时出错","error":"未找到目录: 第七单元 ","parameters":{"studyPhaseCode":"200","studyPhaseName":"初中","subjectCode":"1","subjectName":"语文","textbookVersionCode":"198","textbookVersionName":"部编版(五四制)(2024)","ceciCode":"172","ceciName":"六年级上","gradeCode":"13","catalogCode":"144605","catalogName":"第七单元 ","key":"200_1_198_144605__","id":320}}
{"timestamp":"2025-06-30T11:35:25.731Z","level":"ERROR","instanceId":"instance_15850_1751283311730","message":"❌ 参数处理失败","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"六年级上","catalogName":"第七单元 "},"error":"未找到目录: 第七单元 ","duration":"13.33 seconds"}
{"timestamp":"2025-06-30T11:35:25.760Z","level":"WARN","instanceId":"instance_15850_1751283311730","message":"🔌 浏览器已断开连接"}
{"timestamp":"2025-06-30T11:35:25.830Z","level":"ERROR","instanceId":"instance_15850_1751283311730","message":"❌ Failed operation: process_parameter","operation":"process_parameter","operationId":"op_1751283312404_517t5","phase":"failed","error":"未找到目录: 第七单元 ","paramKey":"200_1_198_144605__"}
{"timestamp":"2025-06-30T11:35:25.836Z","level":"WARN","instanceId":"instance_15850_1751283311730","message":"❌ Parameter failed","paramKey":"200_1_198_144605__","error":"未找到目录: 第七单元 ","attempts":3}
{"timestamp":"2025-06-30T11:35:25.836Z","level":"ERROR","instanceId":"instance_15850_1751283311730","message":"❌ Parameter failed","param":"200_1_198_144605__","error":"未找到目录: 第七单元 "}
{"timestamp":"2025-06-30T11:35:25.836Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🎯 Processing parameter 2/10","param":"200_1_198_149921__"}
{"timestamp":"2025-06-30T11:35:25.836Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🚀 Starting operation: process_parameter","operation":"process_parameter","operationId":"op_1751283325836_4mev5","phase":"start","paramKey":"200_1_198_149921__"}
{"timestamp":"2025-06-30T11:35:25.837Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🎯 开始处理参数","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"六年级下","catalogName":"第四单元活动·探究"}}
{"timestamp":"2025-06-30T11:35:25.837Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📂 设置当前任务组合","combination":"初中/语文/部编版(五四制)(2024)/六年级下/第四单元活动·探究","codes":{"studyPhaseCode":"200","subjectCode":"1","textbookVersionCode":"198","ceciCode":"173","catalogCode":"149921"}}
{"timestamp":"2025-06-30T11:35:25.837Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🔍 正在使用令牌[111]创建浏览器并验证..."}
{"timestamp":"2025-06-30T11:35:26.110Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🌐 浏览器已启动","headless":false,"version":"138.0.7204.23"}
{"timestamp":"2025-06-30T11:35:26.297Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ 已成功创建浏览器和页面并注入令牌","tokenId":111,"pageId":"page_1751283326296_beul6","contextId":"context_1751283326111_d3b5w"}
{"timestamp":"2025-06-30T11:35:26.297Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ 浏览器实例创建成功，开始页面爬取"}
{"timestamp":"2025-06-30T11:35:26.297Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📂 输出目录已创建","encodedPath":"output/%E5%88%9D%E4%B8%AD/%E8%AF%AD%E6%96%87/%E9%83%A8%E7%BC%96%E7%89%88%28%E4%BA%94%E5%9B%9B%E5%88%B6%29%282024%29/%E5%85%AD%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%9B%9B%E5%8D%95%E5%85%83%E6%B4%BB%E5%8A%A8%C2%B7%E6%8E%A2%E7%A9%B6","originalPath":"初中/语文/部编版(五四制)(2024)/六年级下/第四单元活动·探究","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"六年级下","catalogName":"第四单元活动·探究"}}
{"timestamp":"2025-06-30T11:35:30.870Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🔧 配置教材版本和册次设置..."}
{"timestamp":"2025-06-30T11:35:34.314Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📌 选择教材版本: 部编版(五四制)(2024)"}
{"timestamp":"2025-06-30T11:35:34.832Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📌 选择册次: 六年级下"}
{"timestamp":"2025-06-30T11:35:36.725Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🔧 设置持续网络监听以捕获所有API请求"}
{"timestamp":"2025-06-30T11:35:36.726Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ 持续网络监听已设置完成"}
{"timestamp":"2025-06-30T11:35:36.727Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📂 即将点击目录，这将触发第1页的API请求"}
{"timestamp":"2025-06-30T11:35:36.727Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📂 选择目录: 第四单元活动·探究 (将触发第1页API请求)"}
{"timestamp":"2025-06-30T11:35:40.799Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ 目录选择完成: 第四单元活动·探究，第1页API请求已发送"}
{"timestamp":"2025-06-30T11:35:40.800Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ 教材版本、册次和目录设置完成，第1页请求已发送"}
{"timestamp":"2025-06-30T11:35:40.823Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📄 已更新最大页数为: 3"}
{"timestamp":"2025-06-30T11:35:59.110Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ 参数处理完成","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"六年级下","catalogName":"第四单元活动·探究"},"totalCount":22,"pagesProcessed":3,"duration":"33.27 seconds"}
{"timestamp":"2025-06-30T11:35:59.162Z","level":"WARN","instanceId":"instance_15850_1751283311730","message":"🔌 浏览器已断开连接"}
{"timestamp":"2025-06-30T11:35:59.234Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ Completed operation: process_parameter","operation":"process_parameter","operationId":"op_1751283325836_4mev5","phase":"complete","paramKey":"200_1_198_149921__","dataSize":22,"pagesProcessed":3,"savedPath":"output/%E5%88%9D%E4%B8%AD/%E8%AF%AD%E6%96%87/%E9%83%A8%E7%BC%96%E7%89%88%28%E4%BA%94%E5%9B%9B%E5%88%B6%29%282024%29/%E5%85%AD%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%9B%9B%E5%8D%95%E5%85%83%E6%B4%BB%E5%8A%A8%C2%B7%E6%8E%A2%E7%A9%B6"}
{"timestamp":"2025-06-30T11:35:59.242Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ Parameter completed","paramKey":"200_1_198_149921__"}
{"timestamp":"2025-06-30T11:35:59.242Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ Parameter completed","param":"200_1_198_149921__","dataSize":22,"pagesProcessed":3}
{"timestamp":"2025-06-30T11:35:59.242Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🎯 Processing parameter 3/10","param":"200_1_198_149929__"}
{"timestamp":"2025-06-30T11:35:59.242Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🚀 Starting operation: process_parameter","operation":"process_parameter","operationId":"op_1751283359242_31zmk","phase":"start","paramKey":"200_1_198_149929__"}
{"timestamp":"2025-06-30T11:35:59.243Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🎯 开始处理参数","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"六年级下","catalogName":"第五单元"}}
{"timestamp":"2025-06-30T11:35:59.243Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📂 设置当前任务组合","combination":"初中/语文/部编版(五四制)(2024)/六年级下/第五单元","codes":{"studyPhaseCode":"200","subjectCode":"1","textbookVersionCode":"198","ceciCode":"173","catalogCode":"149929"}}
{"timestamp":"2025-06-30T11:35:59.243Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🔍 正在使用令牌[111]创建浏览器并验证..."}
{"timestamp":"2025-06-30T11:35:59.578Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🌐 浏览器已启动","headless":false,"version":"138.0.7204.23"}
{"timestamp":"2025-06-30T11:35:59.809Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ 已成功创建浏览器和页面并注入令牌","tokenId":111,"pageId":"page_1751283359809_19gzz","contextId":"context_1751283359578_l9s04"}
{"timestamp":"2025-06-30T11:35:59.809Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ 浏览器实例创建成功，开始页面爬取"}
{"timestamp":"2025-06-30T11:35:59.810Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📂 输出目录已创建","encodedPath":"output/%E5%88%9D%E4%B8%AD/%E8%AF%AD%E6%96%87/%E9%83%A8%E7%BC%96%E7%89%88%28%E4%BA%94%E5%9B%9B%E5%88%B6%29%282024%29/%E5%85%AD%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E4%BA%94%E5%8D%95%E5%85%83","originalPath":"初中/语文/部编版(五四制)(2024)/六年级下/第五单元","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"六年级下","catalogName":"第五单元"}}
{"timestamp":"2025-06-30T11:36:04.641Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🔧 配置教材版本和册次设置..."}
{"timestamp":"2025-06-30T11:36:08.100Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📌 选择教材版本: 部编版(五四制)(2024)"}
{"timestamp":"2025-06-30T11:36:08.593Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📌 选择册次: 六年级下"}
{"timestamp":"2025-06-30T11:36:10.503Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🔧 设置持续网络监听以捕获所有API请求"}
{"timestamp":"2025-06-30T11:36:10.505Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ 持续网络监听已设置完成"}
{"timestamp":"2025-06-30T11:36:10.505Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📂 即将点击目录，这将触发第1页的API请求"}
{"timestamp":"2025-06-30T11:36:10.506Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📂 选择目录: 第五单元 (将触发第1页API请求)"}
{"timestamp":"2025-06-30T11:36:12.401Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📊 Status Report","parameters":{"total":1539,"completed":46,"pending":1197,"processing":18,"failed":277,"completionRate":"3.0%"},"tokens":{"total":17,"valid":17,"healthy":17},"crawler":{"successRate":"0.0%","averageResponseTime":"0ms"},"health":{"overall":"warning","alerts":1},"processing":{"totalProcessed":2,"successCount":1,"errorCount":1,"currentBatchSize":10}}
{"timestamp":"2025-06-30T11:36:14.584Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ 目录选择完成: 第五单元，第1页API请求已发送"}
{"timestamp":"2025-06-30T11:36:14.585Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ 教材版本、册次和目录设置完成，第1页请求已发送"}
{"timestamp":"2025-06-30T11:36:14.608Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📄 已更新最大页数为: 2"}
{"timestamp":"2025-06-30T11:36:26.328Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ 参数处理完成","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"六年级下","catalogName":"第五单元"},"totalCount":20,"pagesProcessed":2,"duration":"27.09 seconds"}
{"timestamp":"2025-06-30T11:36:26.368Z","level":"WARN","instanceId":"instance_15850_1751283311730","message":"🔌 浏览器已断开连接"}
{"timestamp":"2025-06-30T11:36:26.458Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ Completed operation: process_parameter","operation":"process_parameter","operationId":"op_1751283359242_31zmk","phase":"complete","paramKey":"200_1_198_149929__","dataSize":20,"pagesProcessed":2,"savedPath":"output/%E5%88%9D%E4%B8%AD/%E8%AF%AD%E6%96%87/%E9%83%A8%E7%BC%96%E7%89%88%28%E4%BA%94%E5%9B%9B%E5%88%B6%29%282024%29/%E5%85%AD%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E4%BA%94%E5%8D%95%E5%85%83"}
{"timestamp":"2025-06-30T11:36:26.465Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ Parameter completed","paramKey":"200_1_198_149929__"}
{"timestamp":"2025-06-30T11:36:26.465Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ Parameter completed","param":"200_1_198_149929__","dataSize":20,"pagesProcessed":2}
{"timestamp":"2025-06-30T11:36:26.465Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🎯 Processing parameter 4/10","param":"200_1_198_149935__"}
{"timestamp":"2025-06-30T11:36:26.465Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🚀 Starting operation: process_parameter","operation":"process_parameter","operationId":"op_1751283386465_0npxb","phase":"start","paramKey":"200_1_198_149935__"}
{"timestamp":"2025-06-30T11:36:26.465Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🎯 开始处理参数","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"六年级下","catalogName":"第六单元"}}
{"timestamp":"2025-06-30T11:36:26.466Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📂 设置当前任务组合","combination":"初中/语文/部编版(五四制)(2024)/六年级下/第六单元","codes":{"studyPhaseCode":"200","subjectCode":"1","textbookVersionCode":"198","ceciCode":"173","catalogCode":"149935"}}
{"timestamp":"2025-06-30T11:36:26.466Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🔍 正在使用令牌[111]创建浏览器并验证..."}
{"timestamp":"2025-06-30T11:36:26.750Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🌐 浏览器已启动","headless":false,"version":"138.0.7204.23"}
{"timestamp":"2025-06-30T11:36:26.962Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ 已成功创建浏览器和页面并注入令牌","tokenId":111,"pageId":"page_1751283386962_uglrv","contextId":"context_1751283386750_g51r3"}
{"timestamp":"2025-06-30T11:36:26.962Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ 浏览器实例创建成功，开始页面爬取"}
{"timestamp":"2025-06-30T11:36:26.962Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📂 输出目录已创建","encodedPath":"output/%E5%88%9D%E4%B8%AD/%E8%AF%AD%E6%96%87/%E9%83%A8%E7%BC%96%E7%89%88%28%E4%BA%94%E5%9B%9B%E5%88%B6%29%282024%29/%E5%85%AD%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%85%AD%E5%8D%95%E5%85%83","originalPath":"初中/语文/部编版(五四制)(2024)/六年级下/第六单元","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"六年级下","catalogName":"第六单元"}}
{"timestamp":"2025-06-30T11:36:32.751Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🔧 配置教材版本和册次设置..."}
{"timestamp":"2025-06-30T11:36:35.823Z","level":"ERROR","instanceId":"instance_15850_1751283311730","message":"❌ 设置配置失败","error":"下拉菜单未出现"}
{"timestamp":"2025-06-30T11:36:35.823Z","level":"ERROR","instanceId":"instance_15850_1751283311730","message":"❌ 处理第1页时出错","error":"下拉菜单未出现","parameters":{"studyPhaseCode":"200","studyPhaseName":"初中","subjectCode":"1","subjectName":"语文","textbookVersionCode":"198","textbookVersionName":"部编版(五四制)(2024)","ceciCode":"173","ceciName":"六年级下","gradeCode":"13","catalogCode":"149935","catalogName":"第六单元","key":"200_1_198_149935__","id":325}}
{"timestamp":"2025-06-30T11:36:35.824Z","level":"ERROR","instanceId":"instance_15850_1751283311730","message":"❌ 参数处理失败","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"六年级下","catalogName":"第六单元"},"error":"下拉菜单未出现","duration":"9.36 seconds"}
{"timestamp":"2025-06-30T11:36:35.852Z","level":"WARN","instanceId":"instance_15850_1751283311730","message":"🔌 浏览器已断开连接"}
{"timestamp":"2025-06-30T11:36:35.924Z","level":"ERROR","instanceId":"instance_15850_1751283311730","message":"❌ Failed operation: process_parameter","operation":"process_parameter","operationId":"op_1751283386465_0npxb","phase":"failed","error":"下拉菜单未出现","paramKey":"200_1_198_149935__"}
{"timestamp":"2025-06-30T11:36:35.929Z","level":"WARN","instanceId":"instance_15850_1751283311730","message":"❌ Parameter failed","paramKey":"200_1_198_149935__","error":"下拉菜单未出现","attempts":1}
{"timestamp":"2025-06-30T11:36:35.930Z","level":"ERROR","instanceId":"instance_15850_1751283311730","message":"❌ Parameter failed","param":"200_1_198_149935__","error":"下拉菜单未出现"}
{"timestamp":"2025-06-30T11:36:35.930Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🎯 Processing parameter 5/10","param":"200_1_198_149941__"}
{"timestamp":"2025-06-30T11:36:35.930Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🚀 Starting operation: process_parameter","operation":"process_parameter","operationId":"op_1751283395930_82ya7","phase":"start","paramKey":"200_1_198_149941__"}
{"timestamp":"2025-06-30T11:36:35.930Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🎯 开始处理参数","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"六年级下","catalogName":"第七单元"}}
{"timestamp":"2025-06-30T11:36:35.930Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📂 设置当前任务组合","combination":"初中/语文/部编版(五四制)(2024)/六年级下/第七单元","codes":{"studyPhaseCode":"200","subjectCode":"1","textbookVersionCode":"198","ceciCode":"173","catalogCode":"149941"}}
{"timestamp":"2025-06-30T11:36:35.930Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🔍 正在使用令牌[111]创建浏览器并验证..."}
{"timestamp":"2025-06-30T11:36:36.095Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🌐 浏览器已启动","headless":false,"version":"138.0.7204.23"}
{"timestamp":"2025-06-30T11:36:36.306Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ 已成功创建浏览器和页面并注入令牌","tokenId":111,"pageId":"page_1751283396306_4xcub","contextId":"context_1751283396096_fwyq8"}
{"timestamp":"2025-06-30T11:36:36.306Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ 浏览器实例创建成功，开始页面爬取"}
{"timestamp":"2025-06-30T11:36:36.307Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📂 输出目录已创建","encodedPath":"output/%E5%88%9D%E4%B8%AD/%E8%AF%AD%E6%96%87/%E9%83%A8%E7%BC%96%E7%89%88%28%E4%BA%94%E5%9B%9B%E5%88%B6%29%282024%29/%E5%85%AD%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E4%B8%83%E5%8D%95%E5%85%83","originalPath":"初中/语文/部编版(五四制)(2024)/六年级下/第七单元","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"六年级下","catalogName":"第七单元"}}
{"timestamp":"2025-06-30T11:36:40.860Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🔧 配置教材版本和册次设置..."}
{"timestamp":"2025-06-30T11:36:44.301Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📌 选择教材版本: 部编版(五四制)(2024)"}
{"timestamp":"2025-06-30T11:36:44.820Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📌 选择册次: 六年级下"}
{"timestamp":"2025-06-30T11:36:46.738Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🔧 设置持续网络监听以捕获所有API请求"}
{"timestamp":"2025-06-30T11:36:46.739Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ 持续网络监听已设置完成"}
{"timestamp":"2025-06-30T11:36:46.739Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📂 即将点击目录，这将触发第1页的API请求"}
{"timestamp":"2025-06-30T11:36:46.739Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📂 选择目录: 第七单元 (将触发第1页API请求)"}
{"timestamp":"2025-06-30T11:36:50.807Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ 目录选择完成: 第七单元，第1页API请求已发送"}
{"timestamp":"2025-06-30T11:36:50.808Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ 教材版本、册次和目录设置完成，第1页请求已发送"}
{"timestamp":"2025-06-30T11:36:50.836Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📄 已更新最大页数为: 11"}
{"timestamp":"2025-06-30T11:37:12.403Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"📊 Status Report","parameters":{"total":1539,"completed":47,"pending":1197,"processing":16,"failed":277,"completionRate":"3.1%"},"tokens":{"total":17,"valid":17,"healthy":17},"crawler":{"successRate":"0.0%","averageResponseTime":"0ms"},"health":{"overall":"warning","alerts":1},"processing":{"totalProcessed":4,"successCount":2,"errorCount":2,"currentBatchSize":10}}
{"timestamp":"2025-06-30T11:37:53.476Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🔄 Logger shutting down..."}
{"timestamp":"2025-06-30T11:37:53.476Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🔄 Received SIGINT, initiating graceful shutdown..."}
{"timestamp":"2025-06-30T11:37:53.477Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🔄 Shutting down Scraping Orchestrator..."}
{"timestamp":"2025-06-30T11:37:53.477Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🛑 Stopping scraping process..."}
{"timestamp":"2025-06-30T11:37:53.484Z","level":"ERROR","instanceId":"instance_15850_1751283311730","message":"❌ 分页导航失败: page.waitForTimeout: Target page, context or browser has been closed"}
{"timestamp":"2025-06-30T11:37:53.484Z","level":"ERROR","instanceId":"instance_15850_1751283311730","message":"❌ 导航到第10页失败: page.waitForTimeout: Target page, context or browser has been closed"}
{"timestamp":"2025-06-30T11:37:53.484Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"✅ 参数处理完成","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"六年级下","catalogName":"第七单元"},"totalCount":90,"pagesProcessed":9,"duration":"77.55 seconds"}
{"timestamp":"2025-06-30T11:37:53.503Z","level":"WARN","instanceId":"instance_15850_1751283311730","message":"🔌 浏览器已断开连接"}
{"timestamp":"2025-06-30T11:37:53.522Z","level":"INFO","instanceId":"instance_15850_1751283311730","message":"🔄 Logger shutting down..."}
