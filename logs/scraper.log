{"timestamp":"2025-06-30T10:49:35.198Z","level":"INFO","instanceId":"health_monitor_72766","message":"✅ Health Monitor initialized","components":[]}
{"timestamp":"2025-06-30T10:49:35.543Z","level":"WARN","instanceId":"logger_72766","message":"内存使用率过高: 99.0%","type":"memory","severity":"warning","messageEn":"High memory usage: 99.0%","threshold":10,"actual":98.97,"action":"monitor_only","note":"仅监控模式 - 不会退出实例"}
{"timestamp":"2025-06-30T10:49:35.551Z","level":"INFO","instanceId":"logger_72766","message":"📊 详细内存信息","进程堆内存已用":"3.76 MB","进程堆内存总计":"4.30 MB","进程RSS":"32.81 MB","系统内存总计":"18.00 GB","系统内存已用":"17.82 GB","系统内存空闲":"0.18 GB","系统使用率":"98.97%","进程使用率":"87.54%"}
{"timestamp":"2025-06-30T10:49:35.543Z","level":"WARN","instanceId":"logger_72766","message":"CPU使用率过高: 25.2%","type":"cpu","severity":"warning","messageEn":"High CPU usage: 25.2%","threshold":10,"actual":25.18486646884273,"action":"monitor_only","note":"仅监控模式 - 不会退出实例"}
{"timestamp":"2025-06-30T10:49:35.568Z","level":"INFO","instanceId":"logger_72766","message":"📊 详细CPU信息","用户态CPU":"15.13%","系统态CPU":"10.05%","总CPU使用率":"25.18%","CPU核心数":11,"系统负载":"18.98, 18.47, 16.08"}
{"timestamp":"2025-06-30T10:58:14.773Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"✅ Core components initialized"}
{"timestamp":"2025-06-30T10:58:14.882Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🔧 TokenLifecycleManager configuration","environment":"development","ignoreTokenExpiration":true,"tokensFile":"./tokens.json"}
{"timestamp":"2025-06-30T10:58:14.891Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📥 Tokens loaded successfully","totalTokens":17,"validTokens":17}
{"timestamp":"2025-06-30T10:58:14.893Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🔄 Startup token validation completed","markedValid":17,"total":17,"note":"All tokens with valid JWT structure marked as valid at startup"}
{"timestamp":"2025-06-30T10:58:14.894Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"✅ Token Lifecycle Manager initialized","totalTokens":17,"validTokens":17,"note":"All tokens marked as valid at startup"}
{"timestamp":"2025-06-30T10:58:14.945Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📥 Tokens loaded successfully","totalTokens":17,"validTokens":17}
{"timestamp":"2025-06-30T10:58:14.947Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🔄 Startup token validation completed","markedValid":17,"total":17,"note":"All tokens with valid JWT structure marked as valid at startup"}
{"timestamp":"2025-06-30T10:58:14.948Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"✅ Token Lifecycle Manager initialized","totalTokens":17,"validTokens":17,"note":"All tokens marked as valid at startup"}
{"timestamp":"2025-06-30T10:58:14.963Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📥 Parameters loaded","count":1539}
{"timestamp":"2025-06-30T10:58:14.973Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📥 Parameters loaded","count":1539}
{"timestamp":"2025-06-30T10:58:14.999Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📥 Progress loaded","paramStates":1539,"instances":14}
{"timestamp":"2025-06-30T10:58:15.091Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📥 Progress loaded","paramStates":1539,"instances":14}
{"timestamp":"2025-06-30T10:58:15.201Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📝 Instance registered"}
{"timestamp":"2025-06-30T10:58:15.202Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📝 Instance registered"}
{"timestamp":"2025-06-30T10:58:15.203Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"✅ Parameter State Manager initialized","totalParams":1539,"pendingParams":1208}
{"timestamp":"2025-06-30T10:58:15.204Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"✅ Parameter State Manager initialized","totalParams":1539,"pendingParams":1208}
{"timestamp":"2025-06-30T10:58:15.204Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🚀 正在初始化增强版Playwright爬虫"}
{"timestamp":"2025-06-30T10:58:17.359Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🌐 浏览器已启动","headless":false,"version":"138.0.7204.23"}
{"timestamp":"2025-06-30T10:58:17.367Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"✅ 增强版Playwright爬虫初始化完成"}
{"timestamp":"2025-06-30T10:58:17.368Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"✅ Management components initialized"}
{"timestamp":"2025-06-30T10:58:17.378Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"✅ Health Monitor initialized","components":["tokenManager","parameterManager","crawler"]}
{"timestamp":"2025-06-30T10:58:17.379Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"✅ Monitoring initialized"}
{"timestamp":"2025-06-30T10:58:17.380Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"✅ Scraping Orchestrator initialized successfully"}
{"timestamp":"2025-06-30T10:58:17.381Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🎯 Starting scraping process"}
{"timestamp":"2025-06-30T10:58:17.382Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🔧 Instance count check","activeInstances":6,"maxConcurrentInstances":10,"totalInstances":15,"currentInstanceId":"instance_76459_1751281094768"}
{"timestamp":"2025-06-30T10:58:17.433Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📦 Batch assigned","batchSize":10,"remaining":1188}
{"timestamp":"2025-06-30T10:58:17.436Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📦 Processing batch","batchSize":10,"remaining":1198}
{"timestamp":"2025-06-30T10:58:17.439Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🎯 Processing parameter 1/10","param":"200_1_198_144605__"}
{"timestamp":"2025-06-30T10:58:17.441Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🚀 Starting operation: process_parameter","operation":"process_parameter","operationId":"op_1751281097440_rrjkn","phase":"start","paramKey":"200_1_198_144605__"}
{"timestamp":"2025-06-30T10:58:17.443Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🎯 开始处理参数","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"六年级上","catalogName":"第七单元 "}}
{"timestamp":"2025-06-30T10:58:17.445Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📂 设置当前任务组合","combination":"初中/语文/部编版(五四制)(2024)/六年级上/第七单元 ","codes":{"studyPhaseCode":"200","subjectCode":"1","textbookVersionCode":"198","ceciCode":"172","catalogCode":"144605"}}
{"timestamp":"2025-06-30T10:58:17.448Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🔍 正在使用令牌[111]创建浏览器并验证..."}
{"timestamp":"2025-06-30T10:58:17.485Z","level":"WARN","instanceId":"instance_76459_1751281094768","message":"内存使用率过高: 99.1%","type":"memory","severity":"warning","messageEn":"High memory usage: 99.1%","threshold":80,"actual":99.1,"action":"monitor_only","note":"仅监控模式 - 不会退出实例"}
{"timestamp":"2025-06-30T10:58:17.489Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📊 详细内存信息","进程堆内存已用":"22.90 MB","进程堆内存总计":"37.38 MB","进程RSS":"47.48 MB","系统内存总计":"18.00 GB","系统内存已用":"17.84 GB","系统内存空闲":"0.16 GB","系统使用率":"99.10%","进程使用率":"61.28%"}
{"timestamp":"2025-06-30T10:58:17.508Z","level":"WARN","instanceId":"instance_76459_1751281094768","message":"🔌 浏览器已断开连接"}
{"timestamp":"2025-06-30T10:58:18.196Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🌐 浏览器已启动","headless":false,"version":"138.0.7204.23"}
{"timestamp":"2025-06-30T10:58:19.908Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"✅ 已成功创建浏览器和页面并注入令牌","tokenId":111,"pageId":"page_1751281099907_3oy0b","contextId":"context_1751281098198_6ru8a"}
{"timestamp":"2025-06-30T10:58:19.911Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"✅ 浏览器实例创建成功，开始页面爬取"}
{"timestamp":"2025-06-30T10:58:19.914Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📂 输出目录已创建","encodedPath":"output/%E5%88%9D%E4%B8%AD/%E8%AF%AD%E6%96%87/%E9%83%A8%E7%BC%96%E7%89%88%28%E4%BA%94%E5%9B%9B%E5%88%B6%29%282024%29/%E5%85%AD%E5%B9%B4%E7%BA%A7%E4%B8%8A/%E7%AC%AC%E4%B8%83%E5%8D%95%E5%85%83%20","originalPath":"初中/语文/部编版(五四制)(2024)/六年级上/第七单元 ","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"六年级上","catalogName":"第七单元 "}}
{"timestamp":"2025-06-30T10:58:30.315Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🔧 配置教材版本和册次设置..."}
{"timestamp":"2025-06-30T10:58:34.008Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📌 选择教材版本: 部编版(五四制)(2024)"}
{"timestamp":"2025-06-30T10:58:34.706Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📌 选择册次: 六年级上"}
{"timestamp":"2025-06-30T10:58:36.708Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📂 选择目录: 第七单元 "}
{"timestamp":"2025-06-30T10:58:38.857Z","level":"ERROR","instanceId":"instance_76459_1751281094768","message":"❌ 目录选择失败","error":"未找到目录: 第七单元 "}
{"timestamp":"2025-06-30T10:58:38.859Z","level":"ERROR","instanceId":"instance_76459_1751281094768","message":"❌ 设置配置失败","error":"未找到目录: 第七单元 "}
{"timestamp":"2025-06-30T10:58:38.860Z","level":"ERROR","instanceId":"instance_76459_1751281094768","message":"❌ 处理第1页时出错","error":"未找到目录: 第七单元 ","parameters":{"studyPhaseCode":"200","studyPhaseName":"初中","subjectCode":"1","subjectName":"语文","textbookVersionCode":"198","textbookVersionName":"部编版(五四制)(2024)","ceciCode":"172","ceciName":"六年级上","gradeCode":"13","catalogCode":"144605","catalogName":"第七单元 ","key":"200_1_198_144605__","id":320}}
{"timestamp":"2025-06-30T10:58:38.860Z","level":"ERROR","instanceId":"instance_76459_1751281094768","message":"❌ 参数处理失败","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"六年级上","catalogName":"第七单元 "},"error":"未找到目录: 第七单元 ","duration":"21.42 seconds"}
{"timestamp":"2025-06-30T10:58:39.012Z","level":"WARN","instanceId":"instance_76459_1751281094768","message":"🔌 浏览器已断开连接"}
{"timestamp":"2025-06-30T10:58:39.219Z","level":"ERROR","instanceId":"instance_76459_1751281094768","message":"❌ Failed operation: process_parameter","operation":"process_parameter","operationId":"op_1751281097440_rrjkn","phase":"failed","error":"未找到目录: 第七单元 ","paramKey":"200_1_198_144605__"}
{"timestamp":"2025-06-30T10:58:39.295Z","level":"WARN","instanceId":"instance_76459_1751281094768","message":"❌ Parameter failed","paramKey":"200_1_198_144605__","error":"未找到目录: 第七单元 ","attempts":2}
{"timestamp":"2025-06-30T10:58:39.296Z","level":"ERROR","instanceId":"instance_76459_1751281094768","message":"❌ Parameter failed","param":"200_1_198_144605__","error":"未找到目录: 第七单元 "}
{"timestamp":"2025-06-30T10:58:39.296Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🎯 Processing parameter 2/10","param":"200_1_198_149921__"}
{"timestamp":"2025-06-30T10:58:39.296Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🚀 Starting operation: process_parameter","operation":"process_parameter","operationId":"op_1751281119296_ztaw6","phase":"start","paramKey":"200_1_198_149921__"}
{"timestamp":"2025-06-30T10:58:39.296Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🎯 开始处理参数","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"六年级下","catalogName":"第四单元活动·探究"}}
{"timestamp":"2025-06-30T10:58:39.296Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📂 设置当前任务组合","combination":"初中/语文/部编版(五四制)(2024)/六年级下/第四单元活动·探究","codes":{"studyPhaseCode":"200","subjectCode":"1","textbookVersionCode":"198","ceciCode":"173","catalogCode":"149921"}}
{"timestamp":"2025-06-30T10:58:39.296Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🔍 正在使用令牌[111]创建浏览器并验证..."}
{"timestamp":"2025-06-30T10:58:40.443Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🌐 浏览器已启动","headless":false,"version":"138.0.7204.23"}
{"timestamp":"2025-06-30T10:58:41.173Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"✅ 已成功创建浏览器和页面并注入令牌","tokenId":111,"pageId":"page_1751281121172_z60v5","contextId":"context_1751281120455_ums40"}
{"timestamp":"2025-06-30T10:58:41.175Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"✅ 浏览器实例创建成功，开始页面爬取"}
{"timestamp":"2025-06-30T10:58:41.181Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📂 输出目录已创建","encodedPath":"output/%E5%88%9D%E4%B8%AD/%E8%AF%AD%E6%96%87/%E9%83%A8%E7%BC%96%E7%89%88%28%E4%BA%94%E5%9B%9B%E5%88%B6%29%282024%29/%E5%85%AD%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%9B%9B%E5%8D%95%E5%85%83%E6%B4%BB%E5%8A%A8%C2%B7%E6%8E%A2%E7%A9%B6","originalPath":"初中/语文/部编版(五四制)(2024)/六年级下/第四单元活动·探究","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版(五四制)(2024)","ceciName":"六年级下","catalogName":"第四单元活动·探究"}}
{"timestamp":"2025-06-30T10:58:46.640Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🔧 配置教材版本和册次设置..."}
{"timestamp":"2025-06-30T10:58:50.055Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📌 选择教材版本: 部编版(五四制)(2024)"}
{"timestamp":"2025-06-30T10:58:50.622Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"📌 选择册次: 六年级下"}
{"timestamp":"2025-06-30T10:58:51.299Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🔄 Logger shutting down..."}
{"timestamp":"2025-06-30T10:58:51.301Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🔄 Received SIGINT, initiating graceful shutdown..."}
{"timestamp":"2025-06-30T10:58:51.303Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🔄 Shutting down Scraping Orchestrator..."}
{"timestamp":"2025-06-30T10:58:51.303Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🛑 Stopping scraping process..."}
{"timestamp":"2025-06-30T10:58:51.323Z","level":"INFO","instanceId":"instance_76459_1751281094768","message":"🔄 Logger shutting down..."}
{"timestamp":"2025-06-30T11:10:54.465Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ Core components initialized"}
{"timestamp":"2025-06-30T11:10:54.489Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🔧 TokenLifecycleManager configuration","environment":"development","ignoreTokenExpiration":true,"tokensFile":"./tokens.json"}
{"timestamp":"2025-06-30T11:10:54.490Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📥 Tokens loaded successfully","totalTokens":17,"validTokens":17}
{"timestamp":"2025-06-30T11:10:54.490Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🔄 Startup token validation completed","markedValid":17,"total":17,"note":"All tokens with valid JWT structure marked as valid at startup"}
{"timestamp":"2025-06-30T11:10:54.491Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ Token Lifecycle Manager initialized","totalTokens":17,"validTokens":17,"note":"All tokens marked as valid at startup"}
{"timestamp":"2025-06-30T11:10:54.541Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📥 Tokens loaded successfully","totalTokens":17,"validTokens":17}
{"timestamp":"2025-06-30T11:10:54.541Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🔄 Startup token validation completed","markedValid":17,"total":17,"note":"All tokens with valid JWT structure marked as valid at startup"}
{"timestamp":"2025-06-30T11:10:54.542Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ Token Lifecycle Manager initialized","totalTokens":17,"validTokens":17,"note":"All tokens marked as valid at startup"}
{"timestamp":"2025-06-30T11:10:54.545Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📥 Parameters loaded","count":1539}
{"timestamp":"2025-06-30T11:10:54.547Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📥 Parameters loaded","count":1539}
{"timestamp":"2025-06-30T11:10:54.555Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📥 Progress loaded","paramStates":1539,"instances":15}
{"timestamp":"2025-06-30T11:10:54.561Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📥 Progress loaded","paramStates":1539,"instances":15}
{"timestamp":"2025-06-30T11:10:54.572Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📝 Instance registered"}
{"timestamp":"2025-06-30T11:10:54.572Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📝 Instance registered"}
{"timestamp":"2025-06-30T11:10:54.573Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ Parameter State Manager initialized","totalParams":1539,"pendingParams":1199}
{"timestamp":"2025-06-30T11:10:54.573Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ Parameter State Manager initialized","totalParams":1539,"pendingParams":1199}
{"timestamp":"2025-06-30T11:10:54.573Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🚀 正在初始化增强版Playwright爬虫"}
{"timestamp":"2025-06-30T11:10:55.126Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🌐 浏览器已启动","headless":false,"version":"138.0.7204.23"}
{"timestamp":"2025-06-30T11:10:55.126Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ 增强版Playwright爬虫初始化完成"}
{"timestamp":"2025-06-30T11:10:55.126Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ Management components initialized"}
{"timestamp":"2025-06-30T11:10:55.127Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ Health Monitor initialized","components":["tokenManager","parameterManager","crawler"]}
{"timestamp":"2025-06-30T11:10:55.127Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ Monitoring initialized"}
{"timestamp":"2025-06-30T11:10:55.127Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ Scraping Orchestrator initialized successfully"}
{"timestamp":"2025-06-30T11:10:55.127Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🎯 Starting scraping process"}
{"timestamp":"2025-06-30T11:10:55.127Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🔧 Instance count check","activeInstances":5,"maxConcurrentInstances":10,"totalInstances":16,"currentInstanceId":"instance_6558_1751281854464"}
{"timestamp":"2025-06-30T11:10:55.127Z","level":"WARN","instanceId":"instance_65422_1751279845110","message":"🧹 Cleaned up stale instance","cleanedParams":8}
{"timestamp":"2025-06-30T11:10:55.127Z","level":"WARN","instanceId":"instance_66210_1751279916132","message":"🧹 Cleaned up stale instance","cleanedParams":9}
{"timestamp":"2025-06-30T11:10:55.127Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🧹 Cleanup completed","cleanedParams":16,"processedInstances":2}
{"timestamp":"2025-06-30T11:10:55.134Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📦 Batch assigned","batchSize":10,"remaining":1195}
{"timestamp":"2025-06-30T11:10:55.134Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📦 Processing batch","batchSize":10,"remaining":1205}
{"timestamp":"2025-06-30T11:10:55.134Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🎯 Processing parameter 1/10","param":"200_1_2_103775__"}
{"timestamp":"2025-06-30T11:10:55.134Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🚀 Starting operation: process_parameter","operation":"process_parameter","operationId":"op_1751281855134_wk1vs","phase":"start","paramKey":"200_1_2_103775__"}
{"timestamp":"2025-06-30T11:10:55.135Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🎯 开始处理参数","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版","ceciName":"八年级下","catalogName":"第五单元"}}
{"timestamp":"2025-06-30T11:10:55.135Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📂 设置当前任务组合","combination":"初中/语文/部编版/八年级下/第五单元","codes":{"studyPhaseCode":"200","subjectCode":"1","textbookVersionCode":"2","ceciCode":"82","catalogCode":"103775"}}
{"timestamp":"2025-06-30T11:10:55.135Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🔍 正在使用令牌[111]创建浏览器并验证..."}
{"timestamp":"2025-06-30T11:10:55.153Z","level":"WARN","instanceId":"instance_6558_1751281854464","message":"🔌 浏览器已断开连接"}
{"timestamp":"2025-06-30T11:10:55.229Z","level":"WARN","instanceId":"instance_6558_1751281854464","message":"内存使用率过高: 97.7%","type":"memory","severity":"warning","messageEn":"High memory usage: 97.7%","threshold":80,"actual":97.72,"action":"monitor_only","note":"仅监控模式 - 不会退出实例"}
{"timestamp":"2025-06-30T11:10:55.229Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📊 详细内存信息","进程堆内存已用":"22.87 MB","进程堆内存总计":"37.64 MB","进程RSS":"99.70 MB","系统内存总计":"18.00 GB","系统内存已用":"17.59 GB","系统内存空闲":"0.41 GB","系统使用率":"97.72%","进程使用率":"60.76%"}
{"timestamp":"2025-06-30T11:10:55.385Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🌐 浏览器已启动","headless":false,"version":"138.0.7204.23"}
{"timestamp":"2025-06-30T11:10:55.650Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ 已成功创建浏览器和页面并注入令牌","tokenId":111,"pageId":"page_1751281855650_gju3h","contextId":"context_1751281855386_8ba31"}
{"timestamp":"2025-06-30T11:10:55.651Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ 浏览器实例创建成功，开始页面爬取"}
{"timestamp":"2025-06-30T11:10:55.651Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📂 输出目录已创建","encodedPath":"output/%E5%88%9D%E4%B8%AD/%E8%AF%AD%E6%96%87/%E9%83%A8%E7%BC%96%E7%89%88/%E5%85%AB%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E4%BA%94%E5%8D%95%E5%85%83","originalPath":"初中/语文/部编版/八年级下/第五单元","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版","ceciName":"八年级下","catalogName":"第五单元"}}
{"timestamp":"2025-06-30T11:11:00.716Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🔧 配置教材版本和册次设置..."}
{"timestamp":"2025-06-30T11:11:04.167Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📌 选择教材版本: 部编版"}
{"timestamp":"2025-06-30T11:11:04.684Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📌 选择册次: 八年级下"}
{"timestamp":"2025-06-30T11:11:06.645Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📂 选择目录: 第五单元"}
{"timestamp":"2025-06-30T11:11:10.729Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ 目录选择完成: 第五单元"}
{"timestamp":"2025-06-30T11:11:10.730Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ 教材版本、册次和目录设置完成"}
{"timestamp":"2025-06-30T11:11:10.759Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📄 已更新最大页数为: 57"}
{"timestamp":"2025-06-30T11:11:40.761Z","level":"WARN","instanceId":"instance_6558_1751281854464","message":"⏰ 第1页响应捕获超时，跳过"}
{"timestamp":"2025-06-30T11:11:40.762Z","level":"WARN","instanceId":"instance_6558_1751281854464","message":"⏰ 第1页响应超时"}
{"timestamp":"2025-06-30T11:11:40.763Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ 参数处理完成","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版","ceciName":"八年级下","catalogName":"第五单元"},"totalCount":0,"pagesProcessed":0,"duration":"45.63 seconds"}
{"timestamp":"2025-06-30T11:11:40.816Z","level":"WARN","instanceId":"instance_6558_1751281854464","message":"🔌 浏览器已断开连接"}
{"timestamp":"2025-06-30T11:11:40.898Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ Completed operation: process_parameter","operation":"process_parameter","operationId":"op_1751281855134_wk1vs","phase":"complete","paramKey":"200_1_2_103775__","dataSize":0,"pagesProcessed":0,"savedPath":"output/%E5%88%9D%E4%B8%AD/%E8%AF%AD%E6%96%87/%E9%83%A8%E7%BC%96%E7%89%88/%E5%85%AB%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E4%BA%94%E5%8D%95%E5%85%83"}
{"timestamp":"2025-06-30T11:11:40.906Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ Parameter completed","paramKey":"200_1_2_103775__"}
{"timestamp":"2025-06-30T11:11:40.906Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ Parameter completed","param":"200_1_2_103775__","dataSize":0,"pagesProcessed":0}
{"timestamp":"2025-06-30T11:11:40.906Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🎯 Processing parameter 2/10","param":"200_1_2_103783__"}
{"timestamp":"2025-06-30T11:11:40.906Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🚀 Starting operation: process_parameter","operation":"process_parameter","operationId":"op_1751281900906_fcyha","phase":"start","paramKey":"200_1_2_103783__"}
{"timestamp":"2025-06-30T11:11:40.906Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🎯 开始处理参数","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版","ceciName":"八年级下","catalogName":"第六单元"}}
{"timestamp":"2025-06-30T11:11:40.906Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📂 设置当前任务组合","combination":"初中/语文/部编版/八年级下/第六单元","codes":{"studyPhaseCode":"200","subjectCode":"1","textbookVersionCode":"2","ceciCode":"82","catalogCode":"103783"}}
{"timestamp":"2025-06-30T11:11:40.906Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🔍 正在使用令牌[111]创建浏览器并验证..."}
{"timestamp":"2025-06-30T11:11:41.156Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🌐 浏览器已启动","headless":false,"version":"138.0.7204.23"}
{"timestamp":"2025-06-30T11:11:41.386Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ 已成功创建浏览器和页面并注入令牌","tokenId":111,"pageId":"page_1751281901386_ibwwe","contextId":"context_1751281901157_2xc6l"}
{"timestamp":"2025-06-30T11:11:41.387Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"✅ 浏览器实例创建成功，开始页面爬取"}
{"timestamp":"2025-06-30T11:11:41.387Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📂 输出目录已创建","encodedPath":"output/%E5%88%9D%E4%B8%AD/%E8%AF%AD%E6%96%87/%E9%83%A8%E7%BC%96%E7%89%88/%E5%85%AB%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%85%AD%E5%8D%95%E5%85%83","originalPath":"初中/语文/部编版/八年级下/第六单元","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版","ceciName":"八年级下","catalogName":"第六单元"}}
{"timestamp":"2025-06-30T11:11:46.246Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🔧 配置教材版本和册次设置..."}
{"timestamp":"2025-06-30T11:11:49.658Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📌 选择教材版本: 部编版"}
{"timestamp":"2025-06-30T11:11:55.131Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"📊 Status Report","parameters":{"total":1539,"completed":40,"pending":1205,"processing":18,"failed":276,"completionRate":"2.6%"},"tokens":{"total":17,"valid":17,"healthy":17},"crawler":{"successRate":"0.0%","averageResponseTime":"0ms"},"health":{"overall":"warning","alerts":1},"processing":{"totalProcessed":1,"successCount":1,"errorCount":0,"currentBatchSize":10}}
{"timestamp":"2025-06-30T11:12:37.493Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🔄 Logger shutting down..."}
{"timestamp":"2025-06-30T11:12:37.495Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🔄 Received SIGINT, initiating graceful shutdown..."}
{"timestamp":"2025-06-30T11:12:37.496Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🔄 Shutting down Scraping Orchestrator..."}
{"timestamp":"2025-06-30T11:12:37.496Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🛑 Stopping scraping process..."}
{"timestamp":"2025-06-30T11:12:37.522Z","level":"ERROR","instanceId":"instance_6558_1751281854464","message":"❌ 设置配置失败","error":"locator.click: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - waiting for locator('.ant-dropdown-content').first().locator('.flex_warp').first().locator('.pointer').nth(1)\u001b[22m\n\u001b[2m    - locator resolved to <p data-v-46b589e4=\"\" class=\"pl_10 pr_10 lh_24 ml_20 bd_radius_2 mt_20 pointer bg_shadow_yellow tc_yellow\"> 部编版 </p>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is not stable\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m    - element is not stable\u001b[22m\n\u001b[2m  2 × retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m      - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is not visible\u001b[22m\n\u001b[2m  91 × retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\u001b[2m       - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is not visible\u001b[22m\n\u001b[2m  - retrying click action\u001b[22m\n\u001b[2m    - waiting 500ms\u001b[22m\n"}
{"timestamp":"2025-06-30T11:12:37.524Z","level":"ERROR","instanceId":"instance_6558_1751281854464","message":"❌ 处理第1页时出错","error":"locator.click: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - waiting for locator('.ant-dropdown-content').first().locator('.flex_warp').first().locator('.pointer').nth(1)\u001b[22m\n\u001b[2m    - locator resolved to <p data-v-46b589e4=\"\" class=\"pl_10 pr_10 lh_24 ml_20 bd_radius_2 mt_20 pointer bg_shadow_yellow tc_yellow\"> 部编版 </p>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is not stable\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m    - element is not stable\u001b[22m\n\u001b[2m  2 × retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m      - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is not visible\u001b[22m\n\u001b[2m  91 × retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\u001b[2m       - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is not visible\u001b[22m\n\u001b[2m  - retrying click action\u001b[22m\n\u001b[2m    - waiting 500ms\u001b[22m\n","parameters":{"studyPhaseCode":"200","studyPhaseName":"初中","subjectCode":"1","subjectName":"语文","textbookVersionCode":"2","textbookVersionName":"部编版","ceciCode":"82","ceciName":"八年级下","gradeCode":"8","catalogCode":"103783","catalogName":"第六单元","key":"200_1_2_103783__","id":307}}
{"timestamp":"2025-06-30T11:12:37.526Z","level":"ERROR","instanceId":"instance_6558_1751281854464","message":"❌ 参数处理失败","parameters":{"studyPhaseName":"初中","subjectName":"语文","textbookVersionName":"部编版","ceciName":"八年级下","catalogName":"第六单元"},"error":"locator.click: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - waiting for locator('.ant-dropdown-content').first().locator('.flex_warp').first().locator('.pointer').nth(1)\u001b[22m\n\u001b[2m    - locator resolved to <p data-v-46b589e4=\"\" class=\"pl_10 pr_10 lh_24 ml_20 bd_radius_2 mt_20 pointer bg_shadow_yellow tc_yellow\"> 部编版 </p>\u001b[22m\n\u001b[2m  - attempting click action\u001b[22m\n\u001b[2m    2 × waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is not stable\u001b[22m\n\u001b[2m    - retrying click action\u001b[22m\n\u001b[2m    - waiting 20ms\u001b[22m\n\u001b[2m    - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m    - element is not stable\u001b[22m\n\u001b[2m  2 × retrying click action\u001b[22m\n\u001b[2m      - waiting 100ms\u001b[22m\n\u001b[2m      - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m      - element is not visible\u001b[22m\n\u001b[2m  91 × retrying click action\u001b[22m\n\u001b[2m       - waiting 500ms\u001b[22m\n\u001b[2m       - waiting for element to be visible, enabled and stable\u001b[22m\n\u001b[2m       - element is not visible\u001b[22m\n\u001b[2m  - retrying click action\u001b[22m\n\u001b[2m    - waiting 500ms\u001b[22m\n","duration":"56.62 seconds"}
{"timestamp":"2025-06-30T11:12:37.545Z","level":"WARN","instanceId":"instance_6558_1751281854464","message":"🔌 浏览器已断开连接"}
{"timestamp":"2025-06-30T11:12:37.558Z","level":"INFO","instanceId":"instance_6558_1751281854464","message":"🔄 Logger shutting down..."}
